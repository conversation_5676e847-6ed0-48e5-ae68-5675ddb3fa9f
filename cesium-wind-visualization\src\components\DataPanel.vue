<template>
  <div class="data-panel-drawer" :class="{ expanded: isExpanded }">
    <!-- 抽拉手柄 -->
    <div class="drawer-handle" @click="toggleDrawer">
      <div class="handle-icon">📊</div>
      <div class="handle-text">数据选择</div>
      <div class="handle-arrow" :class="{ rotated: isExpanded }">◀</div>
    </div>

    <!-- 面板内容 -->
    <div class="drawer-content">
      <div class="panel-header">
        <h3 class="text-lg font-semibold">数据选择</h3>
      </div>

      <!-- 台风列表 -->
      <div class="mb-6">
        <h4 class="text-sm font-medium mb-3 text-gray-300">台风列表</h4>
        <div class="space-y-2 max-h-40 overflow-y-auto custom-scrollbar">
          <div
            v-for="typhoon in typhoonStore.typhoons"
            :key="typhoon.id"
            @click="selectTyphoon(typhoon.id)"
            :class="[
              'p-3 rounded-lg cursor-pointer transition-all duration-200',
              typhoonStore.selectedTyphoonId === typhoon.id
                ? 'bg-blue-600 border border-blue-400'
                : 'bg-gray-700 hover:bg-gray-600 border border-gray-600',
            ]">
            <div class="flex justify-between items-start">
              <div>
                <div class="font-medium">{{ typhoon.name }}</div>
                <div class="text-xs text-gray-300">{{ typhoon.year }}年</div>
              </div>
              <div class="text-right">
                <div class="text-xs text-gray-300">{{ typhoon.category }}</div>
                <div class="text-xs text-gray-400">
                  {{ typhoon.maxWindSpeed }}m/s
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-400 mt-1">
              {{ formatDateRange(typhoon.startTime, typhoon.endTime) }}
            </div>
          </div>
        </div>

        <div
          v-if="typhoonStore.isLoading"
          class="text-center py-4 text-gray-400">
          加载中...
        </div>

        <div
          v-if="typhoonStore.error"
          class="text-center py-4 text-red-400 text-sm">
          {{ typhoonStore.error }}
        </div>
      </div>

      <!-- 风场列表 -->
      <div v-if="windStore.hasWindData">
        <h4 class="text-sm font-medium mb-3 text-gray-300">风场数据</h4>

        <!-- 气压层选择 -->
        <div class="mb-3">
          <label class="block text-xs text-gray-400 mb-1">气压层 (hPa)</label>
          <select
            v-model="windStore.selectedPressureLevel"
            @change="onPressureLevelChange"
            class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm text-white focus:border-blue-400 focus:outline-none">
            <option
              v-for="level in windStore.availablePressureLevels"
              :key="level"
              :value="level">
              {{ level }} hPa
            </option>
          </select>
        </div>

        <!-- 时间点列表 -->
        <div class="space-y-1 max-h-60 overflow-y-auto custom-scrollbar">
          <div
            v-for="item in windStore.windDataList"
            :key="item.filename"
            @click="selectWindData(item.time)"
            :class="[
              'p-2 rounded cursor-pointer transition-all duration-200 text-sm',
              windStore.selectedTime === item.time
                ? 'bg-green-600 border border-green-400'
                : 'bg-gray-700 hover:bg-gray-600 border border-gray-600',
            ]">
            <div class="flex justify-between items-center">
              <div>
                <div class="font-medium">{{ item.date }}</div>
                <div class="text-xs text-gray-300">{{ item.hour }}:00 UTC</div>
              </div>
              <div class="text-xs text-gray-400">
                {{ item.pressureLevels.length }} 层
              </div>
            </div>
          </div>
        </div>

        <div v-if="windStore.isLoading" class="text-center py-4 text-gray-400">
          加载风场数据中...
        </div>
      </div>

      <div
        v-else-if="typhoonStore.selectedTyphoon"
        class="text-center py-4 text-gray-400 text-sm">
        请等待风场数据加载...
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useTyphoonStore } from "@/stores/typhoonStore";
import { useWindStore } from "@/stores/windStore";
import { useUIStore } from "@/stores/uiStore";

defineEmits<{
  toggle: [];
}>();

const typhoonStore = useTyphoonStore();
const windStore = useWindStore();
const uiStore = useUIStore();

// 抽拉状态
const isExpanded = ref(false);

// 切换抽拉状态
const toggleDrawer = () => {
  isExpanded.value = !isExpanded.value;
};

// 选择台风
const selectTyphoon = async (typhoonId: string) => {
  try {
    await typhoonStore.selectTyphoon(typhoonId);
    if (typhoonStore.selectedTyphoon) {
      // 加载对应的风场数据列表
      await windStore.loadWindDataList(typhoonId);

      // 初始化时间轴
      uiStore.initializeTimeline(
        typhoonStore.selectedTyphoon.startTime,
        typhoonStore.selectedTyphoon.endTime
      );

      // 自动加载第一个时间点的数据
      if (windStore.windDataList.length > 0) {
        await windStore.loadWindData(
          windStore.windDataList[0].time,
          windStore.selectedPressureLevel
        );
      }
    }
  } catch (error) {
    console.error("Failed to select typhoon:", error);
  }
};

// 选择风场数据
const selectWindData = async (time: string) => {
  try {
    await windStore.loadWindData(time, windStore.selectedPressureLevel);
    uiStore.updateTimelineState({ currentTime: time });
  } catch (error) {
    console.error("Failed to select wind data:", error);
  }
};

// 气压层变化
const onPressureLevelChange = async () => {
  if (windStore.selectedTime) {
    await windStore.loadWindData(
      windStore.selectedTime,
      windStore.selectedPressureLevel
    );
  }
};

// 格式化日期范围
const formatDateRange = (startTime: string, endTime: string) => {
  const start = new Date(startTime);
  const end = new Date(endTime);

  const formatDate = (date: Date) => {
    return `${date.getUTCMonth() + 1}/${date.getUTCDate()}`;
  };

  return `${formatDate(start)} - ${formatDate(end)}`;
};

onMounted(() => {
  // 加载台风列表
  typhoonStore.loadTyphoonList();
});
</script>

<style scoped>
/* 右侧抽拉式面板样式 */
.data-panel-drawer {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 350px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateX(310px);
  transition: transform 0.3s ease;
  z-index: 1000;
  color: white;
}

.data-panel-drawer.expanded {
  transform: translateX(0);
}

.drawer-handle {
  position: absolute;
  top: 50%;
  left: -40px;
  width: 40px;
  height: 120px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-right: none;
  border-radius: 8px 0 0 8px;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.drawer-handle:hover {
  background: rgba(0, 0, 0, 1);
  border-color: rgba(255, 255, 255, 0.3);
}

.handle-icon {
  font-size: 18px;
}

.handle-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  font-size: 12px;
  color: #9ca3af;
}

.handle-arrow {
  font-size: 12px;
  color: #9ca3af;
  transition: transform 0.3s ease;
}

.handle-arrow.rotated {
  transform: rotate(180deg);
}

.drawer-content {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 100px; /* 为底部时间控制面板留空间 */
}

.panel-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 自定义滚动条 */
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.drawer-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.drawer-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.drawer-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-panel-drawer {
    width: 300px;
    transform: translateX(260px);
  }

  .drawer-content {
    padding: 15px;
  }
}
</style>
