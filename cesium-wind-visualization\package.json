{"name": "cesium-wind-visualization", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.13", "cesium": "^1.121.1", "pinia": "^2.2.6", "echarts": "^5.5.1", "tailwindcss": "^3.4.15"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8", "autoprefixer": "^10.4.20", "postcss": "^8.5.0", "vite-plugin-cesium": "^1.2.23"}}