// 时间处理工具函数

/**
 * 将 UTC 时间字符串转换为 Date 对象
 */
export function parseUTCTime(timeString: string): Date {
  return new Date(timeString)
}

/**
 * 将 Date 对象转换为 UTC 时间字符串
 */
export function formatUTCTime(date: Date): string {
  return date.toISOString()
}

/**
 * 格式化时间显示
 */
export function formatTimeDisplay(timeString: string): string {
  const date = parseUTCTime(timeString)
  return date.toISOString().replace('T', ' ').replace('.000Z', ' UTC')
}

/**
 * 计算两个时间之间的小时差
 */
export function getHoursDifference(startTime: string, endTime: string): number {
  const start = parseUTCTime(startTime)
  const end = parseUTCTime(endTime)
  return (end.getTime() - start.getTime()) / (1000 * 60 * 60)
}

/**
 * 在指定时间基础上增加小时数
 */
export function addHours(timeString: string, hours: number): string {
  const date = parseUTCTime(timeString)
  date.setHours(date.getHours() + hours)
  return formatUTCTime(date)
}

/**
 * 获取时间范围内的所有时间点（按小时步长）
 */
export function getTimeRange(startTime: string, endTime: string, stepHours: number = 1): string[] {
  const times: string[] = []
  let currentTime = startTime
  
  while (parseUTCTime(currentTime) <= parseUTCTime(endTime)) {
    times.push(currentTime)
    currentTime = addHours(currentTime, stepHours)
  }
  
  return times
}

/**
 * 检查时间是否在指定范围内
 */
export function isTimeInRange(time: string, startTime: string, endTime: string): boolean {
  const timeDate = parseUTCTime(time)
  const startDate = parseUTCTime(startTime)
  const endDate = parseUTCTime(endTime)
  
  return timeDate >= startDate && timeDate <= endDate
}

/**
 * 获取最接近的时间点
 */
export function getClosestTime(targetTime: string, availableTimes: string[]): string | null {
  if (availableTimes.length === 0) return null
  
  const target = parseUTCTime(targetTime)
  let closest = availableTimes[0]
  let minDiff = Math.abs(parseUTCTime(closest).getTime() - target.getTime())
  
  for (const time of availableTimes) {
    const diff = Math.abs(parseUTCTime(time).getTime() - target.getTime())
    if (diff < minDiff) {
      minDiff = diff
      closest = time
    }
  }
  
  return closest
}

/**
 * 将时间字符串转换为文件名格式
 * 例如: "2019-08-03T00:00:00Z" -> "2019_0803_0000"
 */
export function timeToFilename(timeString: string): string {
  const date = parseUTCTime(timeString)
  const year = date.getUTCFullYear()
  const month = String(date.getUTCMonth() + 1).padStart(2, '0')
  const day = String(date.getUTCDate()).padStart(2, '0')
  const hour = String(date.getUTCHours()).padStart(2, '0')
  
  return `${year}_${month}${day}_${hour}00`
}

/**
 * 从文件名格式转换为时间字符串
 * 例如: "2019_0803_0000" -> "2019-08-03T00:00:00Z"
 */
export function filenameToTime(filename: string): string {
  const match = filename.match(/(\d{4})_(\d{2})(\d{2})_(\d{2})(\d{2})/)
  if (!match) {
    throw new Error(`Invalid filename format: ${filename}`)
  }
  
  const [, year, month, day, hour, minute] = match
  return `${year}-${month}-${day}T${hour}:${minute}:00Z`
}
