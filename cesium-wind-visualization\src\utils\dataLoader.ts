// 数据加载工具函数

import type { TyphoonData, TyphoonListItem } from "@/types/typhoon";
import type { WindDataFile, WindDataListItem } from "@/types/windData";

/**
 * 加载台风列表
 */
export async function loadTyphoonList(): Promise<TyphoonListItem[]> {
  try {
    const response = await fetch("/data/typhoons/list.json");
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Failed to load typhoon list:", error);
    // 返回空数组，不提供硬编码的后备数据
    return [];
  }
}

/**
 * 加载台风详细数据
 */
export async function loadTyphoonData(typhoonId: string): Promise<TyphoonData> {
  try {
    const response = await fetch(`/data/typhoons/${typhoonId}.json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Failed to load typhoon data for ${typhoonId}:`, error);
    throw error;
  }
}

/**
 * 加载风场数据列表
 */
export async function loadWindDataList(
  typhoonId: string
): Promise<WindDataListItem[]> {
  try {
    const response = await fetch(`/data/windfields/${typhoonId}/list.json`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Failed to load wind data list for ${typhoonId}:`, error);
    // 返回空数组，不提供硬编码的后备数据
    return [];
  }
}

/**
 * 加载风场数据文件
 */
export async function loadWindDataFile(
  filePath: string
): Promise<WindDataFile> {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // 将数组数据转换为 Float32Array
    for (const pressureLevel in data.pressureLevels) {
      const levelData = data.pressureLevels[pressureLevel];
      if (levelData.u && Array.isArray(levelData.u.array)) {
        levelData.u.array = new Float32Array(levelData.u.array);
      }
      if (levelData.v && Array.isArray(levelData.v.array)) {
        levelData.v.array = new Float32Array(levelData.v.array);
      }
      if (levelData.speed && Array.isArray(levelData.speed.array)) {
        levelData.speed.array = new Float32Array(levelData.speed.array);
      }
      if (levelData.temperature && Array.isArray(levelData.temperature.array)) {
        levelData.temperature.array = new Float32Array(
          levelData.temperature.array
        );
      }
      if (
        levelData.verticalVelocity &&
        Array.isArray(levelData.verticalVelocity.array)
      ) {
        levelData.verticalVelocity.array = new Float32Array(
          levelData.verticalVelocity.array
        );
      }
    }

    return data;
  } catch (error) {
    console.error(`Failed to load wind data file ${filePath}:`, error);
    throw error;
  }
}

// 移除了硬编码的模拟数据生成函数
// 系统现在完全依赖真实数据文件

/**
 * 预加载数据
 */
export async function preloadData(filePaths: string[]): Promise<void> {
  const promises = filePaths.map(async (path) => {
    try {
      await fetch(path);
    } catch (error) {
      console.warn(`Failed to preload ${path}:`, error);
    }
  });

  await Promise.all(promises);
}

/**
 * 检查数据文件是否存在
 */
export async function checkDataFileExists(filePath: string): Promise<boolean> {
  try {
    const response = await fetch(filePath, { method: "HEAD" });
    return response.ok;
  } catch (error) {
    return false;
  }
}
