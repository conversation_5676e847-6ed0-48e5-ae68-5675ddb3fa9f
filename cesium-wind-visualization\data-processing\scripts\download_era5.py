"""
ERA5数据下载脚本
使用CDS API从Copernicus Climate Data Store下载ERA5再分析数据
"""
import os
import sys
import cdsapi
from datetime import datetime, timedelta
from tqdm import tqdm
import logging
from config import ERA5_CONFIG, TYPHOON_CONFIG, RAW_DATA_DIR, get_era5_filename

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ERA5Downloader:
    def __init__(self):
        """初始化ERA5下载器"""
        try:
            self.client = cdsapi.Client()
            logger.info("CDS API客户端初始化成功")
        except Exception as e:
            logger.error(f"CDS API客户端初始化失败: {e}")
            logger.error("请确保已正确配置CDS API密钥")
            sys.exit(1)
    
    def download_typhoon_data(self, typhoon_id):
        """下载指定台风的ERA5数据"""
        if typhoon_id not in TYPHOON_CONFIG:
            logger.error(f"未找到台风配置: {typhoon_id}")
            return False
        
        config = TYPHOON_CONFIG[typhoon_id]
        logger.info(f"开始下载台风 {config['name']} ({typhoon_id}) 的ERA5数据")
        
        # 解析日期范围
        start_date = datetime.strptime(config['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(config['end_date'], '%Y-%m-%d')
        
        # 生成日期列表
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        # 构建下载请求
        request = {
            "product_type": ["reanalysis"],
            "variable": ERA5_CONFIG['variables'],
            "year": [str(config['year'])],
            "month": [start_date.strftime('%m')],  # 简化为单月，可扩展
            "day": [d.split('-')[2] for d in dates],
            "time": ERA5_CONFIG['times'],
            "pressure_level": ERA5_CONFIG['pressure_levels'],
            "data_format": ERA5_CONFIG['data_format'],
            "download_format": ERA5_CONFIG['download_format'],
            "area": config['area']  # [north, west, south, east]
        }
        
        # 输出文件路径
        output_file = os.path.join(
            RAW_DATA_DIR, 'era5', 
            f"{typhoon_id}_era5_data.grib"
        )
        
        try:
            logger.info("正在提交下载请求...")
            logger.info(f"下载参数: {request}")
            
            # 提交下载请求
            self.client.retrieve(
                ERA5_CONFIG['dataset'],
                request,
                output_file
            )
            
            logger.info(f"数据下载完成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            return False
    
    def download_all_typhoons(self):
        """下载所有配置的台风数据"""
        success_count = 0
        total_count = len(TYPHOON_CONFIG)
        
        for typhoon_id in TYPHOON_CONFIG:
            logger.info(f"处理台风 {typhoon_id} ({success_count + 1}/{total_count})")
            
            if self.download_typhoon_data(typhoon_id):
                success_count += 1
                logger.info(f"台风 {typhoon_id} 下载成功")
            else:
                logger.error(f"台风 {typhoon_id} 下载失败")
        
        logger.info(f"下载完成: {success_count}/{total_count} 个台风数据下载成功")
        return success_count == total_count

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='下载ERA5台风数据')
    parser.add_argument('--typhoon', type=str, help='指定台风ID (如: lekima_2019)')
    parser.add_argument('--all', action='store_true', help='下载所有台风数据')
    
    args = parser.parse_args()
    
    # 确保目录存在
    os.makedirs(os.path.join(RAW_DATA_DIR, 'era5'), exist_ok=True)
    
    downloader = ERA5Downloader()
    
    if args.typhoon:
        # 下载指定台风
        success = downloader.download_typhoon_data(args.typhoon)
        sys.exit(0 if success else 1)
    elif args.all:
        # 下载所有台风
        success = downloader.download_all_typhoons()
        sys.exit(0 if success else 1)
    else:
        # 默认下载利奇马数据
        success = downloader.download_typhoon_data('lekima_2019')
        sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
