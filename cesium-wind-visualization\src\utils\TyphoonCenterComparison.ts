import {
  Viewer,
  Entity,
  Color,
  Cartesian3,
  HorizontalOrigin,
  VerticalOrigin,
} from "cesium";
import type { WindFieldData } from "@/types/windData";
import { VorticityAnalyzer } from "./VorticityAnalyzer";
import { Logger } from "./Logger";

/**
 * 台风中心对比可视化管理器
 * 同时显示观测中心、涡度中心、风速最小中心等多种定位方法
 */
export class TyphoonCenterComparison {
  private viewer: Viewer;
  private entities: Entity[] = [];
  private isVisible: boolean = true;

  constructor(viewer: Viewer) {
    this.viewer = viewer;
  }

  /**
   * 显示台风中心对比
   */
  showComparison(
    observedCenter: { longitude: number; latitude: number; name: string },
    windData: WindFieldData
  ): void {
    this.clearComparison();

    const analysis = VorticityAnalyzer.analyzeTyphoonWindFieldAlignment(
      windData,
      observedCenter.longitude,
      observedCenter.latitude
    );

    // 1. 观测中心（IBTrACS）- 红色
    this.addCenterPoint(
      observedCenter.longitude,
      observedCenter.latitude,
      "观测中心 (IBTrACS)",
      Color.RED,
      "📍"
    );

    // 2. 涡度中心 - 蓝色
    if (analysis.vorticityCenter) {
      this.addCenterPoint(
        analysis.vorticityCenter.longitude,
        analysis.vorticityCenter.latitude,
        `涡度中心 (${analysis.distances.typhoonToVorticity.toFixed(1)}km)`,
        Color.BLUE,
        "🌀"
      );
    }

    // 3. 局部风速最小中心 - 绿色
    if (analysis.localWindSpeedMinimum) {
      this.addCenterPoint(
        analysis.localWindSpeedMinimum.longitude,
        analysis.localWindSpeedMinimum.latitude,
        `台风眼 (${analysis.distances.typhoonToLocalMin.toFixed(1)}km)`,
        Color.GREEN,
        "👁️"
      );
    }

    // 4. 添加连接线显示偏差
    this.addConnectionLines(observedCenter, analysis);

    Logger.success(`台风中心对比已显示，共${this.entities.length}个要素`);
  }

  /**
   * 添加中心点标记
   */
  private addCenterPoint(
    longitude: number,
    latitude: number,
    label: string,
    color: Color,
    emoji: string
  ): void {
    const entity = this.viewer.entities.add({
      position: Cartesian3.fromDegrees(longitude, latitude, 1000),
      point: {
        pixelSize: 12,
        color: color,
        outlineColor: Color.WHITE,
        outlineWidth: 2,
        heightReference: 0,
      },
      label: {
        text: `${emoji} ${label}`,
        font: "11pt sans-serif",
        fillColor: Color.WHITE,
        outlineColor: Color.BLACK,
        outlineWidth: 2,
        horizontalOrigin: HorizontalOrigin.LEFT,
        verticalOrigin: VerticalOrigin.BOTTOM,
        pixelOffset: new Cartesian3(15, -15, 0),
        show: this.isVisible,
      },
      show: this.isVisible,
    });

    this.entities.push(entity);
  }

  /**
   * 添加连接线显示偏差
   */
  private addConnectionLines(
    observedCenter: { longitude: number; latitude: number },
    analysis: any
  ): void {
    // 观测中心到涡度中心的连线
    if (analysis.vorticityCenter) {
      const line = this.viewer.entities.add({
        polyline: {
          positions: [
            Cartesian3.fromDegrees(
              observedCenter.longitude,
              observedCenter.latitude,
              500
            ),
            Cartesian3.fromDegrees(
              analysis.vorticityCenter.longitude,
              analysis.vorticityCenter.latitude,
              500
            ),
          ],
          width: 2,
          material: Color.YELLOW.withAlpha(0.7),
          clampToGround: false,
        },
        show: this.isVisible,
      });
      this.entities.push(line);
    }

    // 观测中心到台风眼的连线
    if (analysis.localWindSpeedMinimum) {
      const line = this.viewer.entities.add({
        polyline: {
          positions: [
            Cartesian3.fromDegrees(
              observedCenter.longitude,
              observedCenter.latitude,
              500
            ),
            Cartesian3.fromDegrees(
              analysis.localWindSpeedMinimum.longitude,
              analysis.localWindSpeedMinimum.latitude,
              500
            ),
          ],
          width: 2,
          material: Color.CYAN.withAlpha(0.7),
          clampToGround: false,
        },
        show: this.isVisible,
      });
      this.entities.push(line);
    }
  }

  /**
   * 切换显示/隐藏
   */
  setVisible(visible: boolean): void {
    this.isVisible = visible;
    this.entities.forEach((entity) => {
      entity.show = visible;
    });
  }

  /**
   * 清除对比显示
   */
  clearComparison(): void {
    this.entities.forEach((entity) => {
      this.viewer.entities.remove(entity);
    });
    this.entities = [];
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.clearComparison();
  }
}

/**
 * 台风中心精度统计
 */
export class TyphoonAccuracyStats {
  private stats: {
    vorticityDistances: number[];
    eyeDistances: number[];
    timestamps: string[];
  } = {
    vorticityDistances: [],
    eyeDistances: [],
    timestamps: [],
  };

  /**
   * 添加统计数据
   */
  addStats(
    timestamp: string,
    vorticityDistance: number,
    eyeDistance: number
  ): void {
    this.stats.timestamps.push(timestamp);
    this.stats.vorticityDistances.push(vorticityDistance);
    this.stats.eyeDistances.push(eyeDistance);
  }

  /**
   * 获取统计摘要
   */
  getSummary(): {
    vorticity: { avg: number; min: number; max: number; count: number };
    eye: { avg: number; min: number; max: number; count: number };
  } {
    // 检查涡度数据
    const vorticityCount = this.stats.vorticityDistances.length;
    let vorticityStats = { avg: 0, min: 0, max: 0, count: 0 };

    if (vorticityCount > 0) {
      const vorticitySum = this.stats.vorticityDistances.reduce(
        (a, b) => a + b,
        0
      );
      vorticityStats = {
        avg: vorticitySum / vorticityCount,
        min: Math.min(...this.stats.vorticityDistances),
        max: Math.max(...this.stats.vorticityDistances),
        count: vorticityCount,
      };
    }

    // 检查台风眼数据
    const eyeCount = this.stats.eyeDistances.length;
    let eyeStats = { avg: 0, min: 0, max: 0, count: 0 };

    if (eyeCount > 0) {
      const eyeSum = this.stats.eyeDistances.reduce((a, b) => a + b, 0);
      eyeStats = {
        avg: eyeSum / eyeCount,
        min: Math.min(...this.stats.eyeDistances),
        max: Math.max(...this.stats.eyeDistances),
        count: eyeCount,
      };
    }

    return {
      vorticity: vorticityStats,
      eye: eyeStats,
    };
  }

  /**
   * 清除统计数据
   */
  clear(): void {
    this.stats = {
      vorticityDistances: [],
      eyeDistances: [],
      timestamps: [],
    };
  }
}
