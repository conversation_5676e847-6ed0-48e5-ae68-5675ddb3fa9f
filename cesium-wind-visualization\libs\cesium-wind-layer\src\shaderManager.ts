import { ShaderSource } from "cesium";
import { updatePositionShader } from "./shaders/updatePosition";
import { calculateSpeedShader } from "./shaders/calculateSpeed";
import { calculateSpeedInterpolatedShader } from "./shaders/calculateSpeedInterpolated";
import { postProcessingPositionFragmentShader } from "./shaders/postProcessingPosition";
import {
  renderParticlesFragmentShader,
  renderParticlesVertexShader,
} from "./shaders/segmentDraw";

export class ShaderManager {
  static getCalculateSpeedShader(): ShaderSource {
    return new ShaderSource({
      sources: [calculateSpeedShader],
    });
  }

  /**
   * ✅ 获取插值版本的速度计算着色器
   * 支持双纹理时间插值
   */
  static getCalculateSpeedInterpolatedShader(): ShaderSource {
    return new ShaderSource({
      sources: [calculateSpeedInterpolatedShader],
    });
  }

  static getUpdatePositionShader(): ShaderSource {
    return new ShaderSource({
      sources: [updatePositionShader],
    });
  }

  static getSegmentDrawVertexShader(): ShaderSource {
    return new ShaderSource({
      sources: [renderParticlesVertexShader],
    });
  }

  static getSegmentDrawFragmentShader(): ShaderSource {
    return new ShaderSource({
      sources: [renderParticlesFragmentShader],
    });
  }

  static getPostProcessingPositionShader(): ShaderSource {
    return new ShaderSource({
      sources: [postProcessingPositionFragmentShader],
    });
  }
}
