import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { WindDataListItem, WindFieldData } from "@/types/windData";
import type { WindLayerOptions } from "cesium-wind-layer/types";
// 移除硬编码的时间控制器导入
// import type { DataState } from "@/types/timeline";
// import { MasterTimeController } from "@/utils/MasterTimeController";
// import { createTimeControllerForTyphoon } from "@/utils/timeControllerFactory";

export const useWindStore = defineStore("wind", () => {
  // 状态
  const windDataList = ref<WindDataListItem[]>([]);
  const currentWindData = ref<WindFieldData | null>(null);
  const selectedPressureLevel = ref<string>("1000");
  const selectedTime = ref<string>("");
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 粒子参数（参考示例应用的优化配置）
  const particleOptions = ref<WindLayerOptions>({
    particlesTextureSize: 64,
    particleHeight: 1000,
    lineWidth: { min: 0.5, max: 2 },
    lineLength: { min: 10, max: 50 },
    speedFactor: 0.8,
    dropRate: 0.003,
    dropRateBump: 0.01,
    colors: [
      "#164B87",
      "#4682B4",
      "#87CEEB",
      "#B0E0E6",
      "#F0F8FF",
      "#FFE4B5",
      "#FFA500",
      "#FF6347",
      "#DC143C",
    ],
    flipY: false,
    useViewerBounds: true,
    dynamic: true,
  });

  // 计算属性
  const hasWindData = computed(() => windDataList.value.length > 0);
  const availablePressureLevels = computed(() => {
    const levels = new Set<string>();
    windDataList.value.forEach((item) => {
      item.pressureLevels.forEach((level) => levels.add(level));
    });
    return Array.from(levels).sort((a, b) => parseInt(b) - parseInt(a));
  });

  const availableTimes = computed(() => {
    return windDataList.value.map((item) => item.time).sort();
  });

  // 动作
  const loadWindDataList = async (typhoonId: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      // 尝试加载真实的数据列表
      const response = await fetch(`/data/windfields/${typhoonId}/list.json`);
      if (response.ok) {
        windDataList.value = await response.json();
      } else {
        // 如果没有list.json，返回空数组 - 不提供硬编码的后备数据
        console.warn(
          `未找到 ${typhoonId} 的数据列表文件: /data/windfields/${typhoonId}/list.json`
        );
        windDataList.value = [];
      }

      // 默认选择第一个时间点
      if (windDataList.value.length > 0) {
        selectedTime.value = windDataList.value[0].time;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : "加载风场数据列表失败";
      console.error("Failed to load wind data list:", err);
    } finally {
      isLoading.value = false;
    }
  };

  const loadWindData = async (time: string, pressureLevel: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      // 根据时间找到对应的数据文件
      const dataItem = windDataList.value.find((item) => item.time === time);
      if (!dataItem) {
        throw new Error(`找不到时间 ${time} 对应的数据文件`);
      }

      // 加载风场数据文件
      const response = await fetch(dataItem.path);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const rawData = await response.json();

      // 使用公共函数处理数据
      const windData = processWindFieldData(rawData, pressureLevel);
      if (!windData) {
        throw new Error(`处理气压层 ${pressureLevel} 数据失败`);
      }

      currentWindData.value = windData;
      selectedTime.value = time;
      selectedPressureLevel.value = pressureLevel;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "加载风场数据失败";
      console.error("Failed to load wind data:", err);
    } finally {
      isLoading.value = false;
    }
  };

  const updateParticleOptions = (options: Partial<WindLayerOptions>) => {
    particleOptions.value = { ...particleOptions.value, ...options };
  };

  const resetParticleOptions = () => {
    particleOptions.value = {
      particlesTextureSize: 64,
      particleHeight: 1000,
      lineWidth: { min: 0.5, max: 2 },
      lineLength: { min: 10, max: 50 },
      speedFactor: 0.8,
      dropRate: 0.003,
      dropRateBump: 0.01,
      colors: [
        "#164B87",
        "#4682B4",
        "#87CEEB",
        "#B0E0E6",
        "#F0F8FF",
        "#FFE4B5",
        "#FFA500",
        "#FF6347",
        "#DC143C",
      ],
      flipY: false,
      useViewerBounds: true,
      dynamic: true,
    };
  };

  // 提取公共函数：处理风场数据
  const processWindFieldData = (
    rawData: any,
    pressureLevel: string
  ): WindFieldData | null => {
    try {
      // 检查数据格式
      let levelData;
      if (rawData.pressureLevels && rawData.pressureLevels[pressureLevel]) {
        // 新格式：包含多个气压层
        levelData = rawData.pressureLevels[pressureLevel];
      } else if (rawData.u && rawData.v) {
        // 旧格式：单个气压层
        levelData = rawData;
      } else {
        console.warn(`数据中没有找到气压层 ${pressureLevel}`);
        return null;
      }

      // 计算风速范围
      const uArray = new Float32Array(levelData.u.array);
      const vArray = new Float32Array(levelData.v.array);
      let speedMin = Infinity;
      let speedMax = -Infinity;

      for (let i = 0; i < uArray.length; i++) {
        const speed = Math.sqrt(uArray[i] * uArray[i] + vArray[i] * vArray[i]);
        if (speed < speedMin) speedMin = speed;
        if (speed > speedMax) speedMax = speed;
      }

      // 转换数据格式为WindFieldData（包含speed字段）
      const windData: WindFieldData = {
        u: {
          array: uArray,
          min: levelData.u.min,
          max: levelData.u.max,
        },
        v: {
          array: vArray,
          min: levelData.v.min,
          max: levelData.v.max,
        },
        speed: {
          array: new Float32Array(0), // 不需要speed数组，只需要min/max
          min: speedMin,
          max: speedMax,
        },
        width: levelData.width,
        height: levelData.height,
        bounds: rawData.bounds || {
          west: levelData.bbox[0],
          south: levelData.bbox[1],
          east: levelData.bbox[2],
          north: levelData.bbox[3],
        },
      };

      return windData;
    } catch (err) {
      console.error("处理风场数据失败:", err);
      return null;
    }
  };

  // 预加载风场数据（用于插值）
  const preloadWindData = async (
    time: string,
    pressureLevel: string
  ): Promise<WindFieldData | null> => {
    try {
      // 根据时间找到对应的数据文件
      const dataItem = windDataList.value.find((item) => item.time === time);
      if (!dataItem) {
        console.warn(`找不到时间 ${time} 对应的数据文件`);
        return null;
      }

      // 加载风场数据文件
      const response = await fetch(dataItem.path);
      if (!response.ok) {
        console.warn(`预加载数据失败: ${dataItem.path}`);
        return null;
      }

      const rawData = await response.json();

      // 使用公共函数处理数据
      const windData = processWindFieldData(rawData, pressureLevel);
      if (windData) {
        console.log(`预加载成功: ${time}`);
      }
      return windData;
    } catch (err) {
      console.error("预加载风场数据失败:", err);
      return null;
    }
  };

  return {
    // 状态
    windDataList,
    currentWindData,
    selectedPressureLevel,
    selectedTime,
    isLoading,
    error,
    particleOptions,

    // 计算属性
    hasWindData,
    availablePressureLevels,
    availableTimes,

    // 动作
    loadWindDataList,
    loadWindData,
    preloadWindData,
    updateParticleOptions,
    resetParticleOptions,
  };
});
