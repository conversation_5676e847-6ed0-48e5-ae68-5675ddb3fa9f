import {
  PixelDatatype,
  PixelFormat,
  Sampler,
  Texture,
  TextureMagnificationFilter,
  TextureMinificationFilter,
  Cartesian2,
} from "cesium";
import { WindLayerOptions, WindData, InterpolationState } from "./types";
import { ShaderManager } from "./shaderManager";
import CustomPrimitive from "./customPrimitive";
import { WindParticlesComputing } from "./windParticlesComputing";

/**
 * ✅ 增强版风场粒子计算类 - 支持时间插值
 * 继承自原始类，添加双纹理插值功能
 */
export class WindParticlesComputingInterpolated extends WindParticlesComputing {
  // 插值状态
  private interpolationState: InterpolationState | null = null;

  // 双纹理系统
  private windTexturesNext!: {
    U: Texture;
    V: Texture;
  };

  // 插值因子
  private interpolationFactor: number = 0;

  // 是否启用插值模式
  private interpolationEnabled: boolean = false;

  constructor(
    context: any,
    windData: Required<WindData>,
    options: WindLayerOptions,
    viewerParameters: any,
    scene: any
  ) {
    super(context, windData, options, viewerParameters, scene);

    // 检查是否启用插值
    this.interpolationEnabled = options.enableInterpolation ?? false;

    if (this.interpolationEnabled) {
      this.createNextWindTextures();
      this.recreateComputingPrimitivesWithInterpolation();
    }
  }

  /**
   * ✅ 创建下一时间点的风场纹理
   */
  private createNextWindTextures() {
    const options = {
      context: this.context,
      width: this.windData.width,
      height: this.windData.height,
      pixelFormat: PixelFormat.RED,
      pixelDatatype: PixelDatatype.FLOAT,
      flipY: this.options.flipY ?? false,
      sampler: new Sampler({
        minificationFilter: TextureMinificationFilter.LINEAR,
        magnificationFilter: TextureMagnificationFilter.LINEAR,
      }),
    };

    // 初始时使用相同的数据，避免未定义的纹理
    this.windTexturesNext = {
      U: new Texture({
        ...options,
        source: {
          arrayBufferView: new Float32Array(this.windData.u.array),
        },
      }),
      V: new Texture({
        ...options,
        source: {
          arrayBufferView: new Float32Array(this.windData.v.array),
        },
      }),
    };
  }

  /**
   * ✅ 重新创建支持插值的计算原语
   */
  private recreateComputingPrimitivesWithInterpolation() {
    // 销毁原有的calculateSpeed原语
    if (this.primitives.calculateSpeed) {
      this.primitives.calculateSpeed.destroy();
    }

    // 创建新的插值版本
    this.primitives.calculateSpeed = new CustomPrimitive({
      commandType: "Compute",
      uniformMap: {
        // ✅ 双纹理uniform映射
        U_current: () => this.windTextures.U,
        V_current: () => this.windTextures.V,
        U_next: () => this.windTexturesNext.U,
        V_next: () => this.windTexturesNext.V,
        u_interpolationFactor: () => this.interpolationFactor,

        // 保持原有的uniform
        uRange: () => new Cartesian2(this.windData.u.min, this.windData.u.max),
        vRange: () => new Cartesian2(this.windData.v.min, this.windData.v.max),
        speedRange: () =>
          new Cartesian2(this.windData.speed.min, this.windData.speed.max),
        currentParticlesPosition: () =>
          this.particlesTextures.currentParticlesPosition,
        speedScaleFactor: () => {
          return (
            (this.viewerParameters.pixelSize + 50) * this.options.speedFactor
          );
        },
        frameRateAdjustment: () => this.frameRateAdjustment,
        dimension: () =>
          new Cartesian2(this.windData.width, this.windData.height),
        minimum: () =>
          new Cartesian2(this.windData.bounds.west, this.windData.bounds.south),
        maximum: () =>
          new Cartesian2(this.windData.bounds.east, this.windData.bounds.north),
      },
      fragmentShaderSource: ShaderManager.getCalculateSpeedInterpolatedShader(),
      outputTexture: this.particlesTextures.particlesSpeed,
      preExecute: () => {
        const temp = this.particlesTextures.previousParticlesPosition;
        this.particlesTextures.previousParticlesPosition =
          this.particlesTextures.currentParticlesPosition;
        this.particlesTextures.currentParticlesPosition =
          this.particlesTextures.postProcessingPosition;
        this.particlesTextures.postProcessingPosition = temp;
        if (this.primitives.calculateSpeed.commandToExecute) {
          this.primitives.calculateSpeed.commandToExecute.outputTexture =
            this.particlesTextures.particlesSpeed;
        }
      },
      isDynamic: () => this.options.dynamic,
    });
  }

  /**
   * ✅ 设置插值状态
   * 这是与MasterTimeController集成的关键方法
   */
  setInterpolationState(state: InterpolationState): void {
    if (!this.interpolationEnabled) {
      console.warn(
        "Interpolation is not enabled. Enable it in WindLayerOptions."
      );
      return;
    }

    this.interpolationState = state;

    // 更新当前时间点的纹理
    this.updateCurrentWindTextures(state.currentData);

    // 更新下一时间点的纹理
    this.updateNextWindTextures(state.nextData);

    // 更新插值因子
    this.interpolationFactor = state.interpolationFactor;
  }

  /**
   * ✅ 更新当前时间点的风场纹理
   */
  private updateCurrentWindTextures(windData: Required<WindData>) {
    // 销毁旧纹理
    this.windTextures.U.destroy();
    this.windTextures.V.destroy();

    // 更新风场数据
    this.windData = windData;

    // 重新创建纹理
    this.createWindTextures();
  }

  /**
   * ✅ 更新下一时间点的风场纹理
   */
  private updateNextWindTextures(windData: Required<WindData>) {
    // 销毁旧纹理
    this.windTexturesNext.U.destroy();
    this.windTexturesNext.V.destroy();

    const options = {
      context: this.context,
      width: windData.width,
      height: windData.height,
      pixelFormat: PixelFormat.RED,
      pixelDatatype: PixelDatatype.FLOAT,
      flipY: this.options.flipY ?? false,
      sampler: new Sampler({
        minificationFilter: TextureMinificationFilter.LINEAR,
        magnificationFilter: TextureMagnificationFilter.LINEAR,
      }),
    };

    this.windTexturesNext = {
      U: new Texture({
        ...options,
        source: {
          arrayBufferView: new Float32Array(windData.u.array),
        },
      }),
      V: new Texture({
        ...options,
        source: {
          arrayBufferView: new Float32Array(windData.v.array),
        },
      }),
    };
  }

  /**
   * ✅ 仅更新插值因子（高频调用，性能优化）
   */
  updateInterpolationFactor(factor: number): void {
    if (!this.interpolationEnabled) return;

    this.interpolationFactor = Math.max(0, Math.min(1, factor));
  }

  /**
   * ✅ 获取当前插值状态
   */
  getInterpolationState(): InterpolationState | null {
    return this.interpolationState;
  }

  /**
   * ✅ 启用/禁用插值模式
   */
  setInterpolationEnabled(enabled: boolean): void {
    if (enabled === this.interpolationEnabled) return;

    this.interpolationEnabled = enabled;

    if (enabled) {
      this.createNextWindTextures();
      this.recreateComputingPrimitivesWithInterpolation();
    } else {
      // 恢复到原始模式
      this.destroyNextWindTextures();
      this.createComputingPrimitives(); // 调用父类方法
    }
  }

  /**
   * ✅ 销毁下一时间点的纹理
   */
  private destroyNextWindTextures() {
    if (this.windTexturesNext) {
      Object.values(this.windTexturesNext).forEach((texture) =>
        texture.destroy()
      );
    }
  }

  /**
   * ✅ 重写销毁方法
   */
  destroy() {
    this.destroyNextWindTextures();
    super.destroy();
  }

  /**
   * ✅ 获取性能统计信息
   */
  getPerformanceStats(): {
    interpolationEnabled: boolean;
    interpolationFactor: number;
    textureMemoryUsage: number; // 估算的纹理内存使用量（字节）
  } {
    const textureSize = this.windData.width * this.windData.height * 4; // Float32 = 4 bytes
    const baseMemory = textureSize * 2; // U + V 纹理
    const interpolationMemory = this.interpolationEnabled ? textureSize * 2 : 0; // 额外的 U_next + V_next

    return {
      interpolationEnabled: this.interpolationEnabled,
      interpolationFactor: this.interpolationFactor,
      textureMemoryUsage: baseMemory + interpolationMemory,
    };
  }
}
