"""
ERA5数据处理脚本
将GRIB格式的ERA5数据转换为JSON格式，用于前端可视化
"""

import os
import sys
import json
import numpy as np
import xarray as xr
import pandas as pd
from datetime import datetime, timedelta
import logging
from tqdm import tqdm
from config import (
    RAW_DATA_DIR,
    PROCESSED_DATA_DIR,
    PUBLIC_DATA_DIR,
    TYPHOON_CONFIG,
    PROCESSING_CONFIG,
    get_windfield_filename,
)

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ERA5Processor:
    def __init__(self):
        """初始化ERA5处理器"""
        self.pressure_levels = [1000, 850, 500, 200]  # hPa
        self.variables = ["u", "v", "t", "w"]  # u风分量, v风分量, 温度, 垂直速度

    def load_era5_data(self, grib_file):
        """加载ERA5 GRIB数据"""
        try:
            logger.info(f"加载ERA5数据: {grib_file}")

            # 使用xarray加载GRIB数据
            ds = xr.open_dataset(grib_file, engine="cfgrib")
            logger.info(f"数据维度: {ds.dims}")
            logger.info(f"数据变量: {list(ds.data_vars)}")

            return ds

        except Exception as e:
            logger.error(f"加载ERA5数据失败: {e}")
            return None

    def process_windfield_data(self, ds, time_point, pressure_level):
        """处理单个时间点和气压层的风场数据"""
        try:
            # 选择指定时间和气压层的数据
            data_slice = ds.sel(time=time_point, isobaricInhPa=pressure_level)

            # 提取风分量数据
            u_data = data_slice["u"].values  # u风分量 (m/s)
            v_data = data_slice["v"].values  # v风分量 (m/s)

            # 获取坐标信息
            lats = data_slice["latitude"].values
            lons = data_slice["longitude"].values

            # 确保数据是2D数组
            if u_data.ndim == 0:
                u_data = np.array([[u_data]])
                v_data = np.array([[v_data]])
            elif u_data.ndim == 1:
                u_data = u_data.reshape(1, -1)
                v_data = v_data.reshape(1, -1)

            # 翻转纬度数据（从北到南变为从南到北）
            if lats[0] > lats[-1]:
                lats = lats[::-1]
                u_data = u_data[::-1, :]
                v_data = v_data[::-1, :]

            # 计算边界框
            bbox = [
                float(lons.min()),  # west
                float(lats.min()),  # south
                float(lons.max()),  # east
                float(lats.max()),  # north
            ]

            # 展平数据为1D数组
            u_flat = u_data.flatten()
            v_flat = v_data.flatten()

            # 处理无效值
            valid_mask = ~(np.isnan(u_flat) | np.isnan(v_flat))
            u_flat = np.where(valid_mask, u_flat, 0.0)
            v_flat = np.where(valid_mask, v_flat, 0.0)

            # 构建输出数据结构
            windfield_data = {
                "bbox": bbox,
                "width": len(lons),
                "height": len(lats),
                "unit": "m s-1",
                "name": f"wind_field_{pressure_level}hPa",
                "time": int(pd.Timestamp(time_point).timestamp() * 1000),  # 毫秒时间戳
                "pressure_level": pressure_level,
                "noDataValue": 1e20,
                "u": {
                    "array": u_flat.tolist(),
                    "min": float(u_flat.min()),
                    "max": float(u_flat.max()),
                },
                "v": {
                    "array": v_flat.tolist(),
                    "min": float(v_flat.min()),
                    "max": float(v_flat.max()),
                },
            }

            # 如果有温度数据，也包含进去
            if "t" in data_slice:
                t_data = data_slice["t"].values
                if t_data.ndim == 0:
                    t_data = np.array([[t_data]])
                elif t_data.ndim == 1:
                    t_data = t_data.reshape(1, -1)

                if lats[0] > lats[-1]:
                    t_data = t_data[::-1, :]

                t_flat = t_data.flatten()
                t_flat = np.where(~np.isnan(t_flat), t_flat, 273.15)  # 默认0°C

                windfield_data["temperature"] = {
                    "array": t_flat.tolist(),
                    "min": float(t_flat.min()),
                    "max": float(t_flat.max()),
                    "unit": "K",
                }

            return windfield_data

        except Exception as e:
            logger.error(f"处理风场数据失败: {e}")
            return None

    def process_typhoon_era5(self, typhoon_id):
        """处理指定台风的ERA5数据"""
        if typhoon_id not in TYPHOON_CONFIG:
            logger.error(f"未找到台风配置: {typhoon_id}")
            return False

        config = TYPHOON_CONFIG[typhoon_id]
        grib_file = os.path.join(RAW_DATA_DIR, "era5", f"{typhoon_id}_era5_data.grib")

        if not os.path.exists(grib_file):
            logger.error(f"ERA5数据文件不存在: {grib_file}")
            return False

        # 加载数据
        ds = self.load_era5_data(grib_file)
        if ds is None:
            return False

        logger.info(f"开始处理台风 {config['name']} 的ERA5数据")

        # 获取时间序列
        times = pd.to_datetime(ds.time.values)

        # 创建输出目录
        output_dir = os.path.join(PUBLIC_DATA_DIR, "windfields", typhoon_id)
        os.makedirs(output_dir, exist_ok=True)

        # 生成数据文件列表
        data_list = []

        # 处理每个时间点
        for time_point in tqdm(times, desc="处理时间点"):
            time_str = time_point.strftime("%Y-%m-%d %H:%M:%S")

            # 处理所有4个气压层的数据
            pressure_levels_data = {}
            available_levels = []

            for pressure_level in self.pressure_levels:
                windfield_data = self.process_windfield_data(
                    ds, time_point, pressure_level
                )
                if windfield_data is not None:
                    pressure_levels_data[str(pressure_level)] = windfield_data
                    available_levels.append(str(pressure_level))

            if not pressure_levels_data:
                logger.warning(f"跳过时间点 {time_str}：无有效数据")
                continue

            # 构建完整的风场数据文件
            complete_windfield_data = {
                "time": time_point.isoformat() + "Z",
                "timestamp": int(time_point.timestamp() * 1000),  # 毫秒时间戳
                "pressureLevels": pressure_levels_data,
                "bounds": {
                    "west": pressure_levels_data[available_levels[0]]["bbox"][0],
                    "south": pressure_levels_data[available_levels[0]]["bbox"][1],
                    "east": pressure_levels_data[available_levels[0]]["bbox"][2],
                    "north": pressure_levels_data[available_levels[0]]["bbox"][3],
                },
                "resolution": {
                    "width": pressure_levels_data[available_levels[0]]["width"],
                    "height": pressure_levels_data[available_levels[0]]["height"],
                },
                "availableLevels": available_levels,
                "unit": "m s-1",
                "source": "ERA5",
            }

            # 生成文件名
            filename = get_windfield_filename(
                typhoon_id, time_point.date(), time_point.hour
            )

            # 保存JSON文件
            output_file = os.path.join(output_dir, filename)
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(complete_windfield_data, f, ensure_ascii=False, indent=2)

            # 添加到数据列表
            data_list.append(
                {
                    "filename": filename,
                    "time": time_point.isoformat() + "Z",
                    "date": time_point.strftime("%Y-%m-%d"),
                    "hour": f"{time_point.hour:02d}",
                    "pressureLevels": available_levels,
                    "path": f"/data/windfields/{typhoon_id}/{filename}",
                }
            )

            logger.info(f"已处理: {filename} (包含 {len(available_levels)} 个气压层)")

        # 保存数据列表
        list_file = os.path.join(output_dir, "list.json")
        with open(list_file, "w", encoding="utf-8") as f:
            json.dump(data_list, f, ensure_ascii=False, indent=2)

        logger.info(f"处理完成，共生成 {len(data_list)} 个风场数据文件")
        logger.info(f"数据列表保存至: {list_file}")

        return True


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="处理ERA5风场数据")
    parser.add_argument("--typhoon", type=str, help="指定台风ID")
    parser.add_argument("--all", action="store_true", help="处理所有台风数据")

    args = parser.parse_args()

    processor = ERA5Processor()

    if args.typhoon:
        success = processor.process_typhoon_era5(args.typhoon)
    elif args.all:
        success_count = 0
        for typhoon_id in TYPHOON_CONFIG:
            if processor.process_typhoon_era5(typhoon_id):
                success_count += 1
        success = success_count == len(TYPHOON_CONFIG)
    else:
        # 默认处理利奇马数据
        success = processor.process_typhoon_era5("lekima_2019")

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
