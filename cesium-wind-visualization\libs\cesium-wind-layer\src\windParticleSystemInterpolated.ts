import { WindLayerOptions, WindData, InterpolationState } from "./types";
import { WindParticleSystem } from "./windParticleSystem";
import { WindParticlesComputingInterpolated } from "./windParticlesComputingInterpolated";
import { WindParticlesRendering } from "./windParticlesRendering";

/**
 * ✅ 增强版风场粒子系统 - 支持时间插值
 * 继承自原始粒子系统，使用插值版本的计算组件
 */
export class WindParticleSystemInterpolated extends WindParticleSystem {
  // 插值版本的计算组件
  computing: WindParticlesComputingInterpolated;

  constructor(
    context: any,
    windData: Required<WindData>,
    options: WindLayerOptions,
    viewerParameters: any,
    scene: any
  ) {
    // 调用父类构造函数
    super(context, windData, options, viewerParameters, scene);

    // 现在安全地替换computing组件
    try {
      // 保存原有computing的配置
      const originalComputing = this.computing;

      // 销毁原有computing
      if (
        originalComputing &&
        typeof originalComputing.destroy === "function"
      ) {
        originalComputing.destroy();
      }

      // 创建插值版本的computing组件
      this.computing = new WindParticlesComputingInterpolated(
        context,
        windData,
        options,
        viewerParameters,
        scene
      );

      // 重新创建rendering组件以使用新的computing
      if (this.rendering && typeof this.rendering.destroy === "function") {
        this.rendering.destroy();
      }
      this.rendering = new WindParticlesRendering(
        context,
        options,
        viewerParameters,
        this.computing
      );

      console.log("WindParticleSystemInterpolated 插值组件替换成功");
    } catch (error) {
      console.error("插值组件替换失败，使用标准实现:", error);
      // 如果失败，保持使用父类的标准实现
    }
  }

  /**
   * ✅ 设置插值状态
   */
  setInterpolationState(state: InterpolationState): void {
    if (
      this.computing &&
      typeof this.computing.setInterpolationState === "function"
    ) {
      this.computing.setInterpolationState(state);
      console.log("插值状态已设置:", state);
    } else {
      console.warn("Computing组件不支持插值状态设置");
    }
  }

  /**
   * ✅ 更新插值因子
   */
  updateInterpolationFactor(factor: number): void {
    if (
      this.computing &&
      typeof this.computing.updateInterpolationFactor === "function"
    ) {
      this.computing.updateInterpolationFactor(factor);
      console.log("插值因子已更新:", factor);
    } else {
      console.warn("Computing组件不支持插值因子更新");
    }
  }

  /**
   * ✅ 获取插值状态
   */
  getInterpolationState(): InterpolationState | null {
    if (
      this.computing &&
      typeof this.computing.getInterpolationState === "function"
    ) {
      return this.computing.getInterpolationState();
    }
    return null;
  }

  /**
   * ✅ 启用/禁用插值模式
   */
  setInterpolationEnabled(enabled: boolean): void {
    if (
      this.computing &&
      typeof this.computing.setInterpolationEnabled === "function"
    ) {
      this.computing.setInterpolationEnabled(enabled);
      console.log("插值模式已设置:", enabled);
    } else {
      console.warn("Computing组件不支持插值模式切换");
    }
  }

  /**
   * ✅ 获取性能统计
   */
  getPerformanceStats(): {
    interpolationEnabled: boolean;
    interpolationFactor: number;
    textureMemoryUsage: number;
  } {
    if (
      this.computing &&
      typeof this.computing.getPerformanceStats === "function"
    ) {
      return this.computing.getPerformanceStats();
    }
    return {
      interpolationEnabled: false,
      interpolationFactor: 0,
      textureMemoryUsage: 0,
    };
  }

  /**
   * ✅ 重写changeOptions以支持插值选项
   */
  changeOptions(options: Partial<WindLayerOptions>): void {
    // 检查是否需要切换插值模式
    if (options.enableInterpolation !== undefined) {
      this.setInterpolationEnabled(options.enableInterpolation);
    }

    // 调用父类方法处理其他选项
    super.changeOptions(options);
  }

  /**
   * ✅ 重写destroy方法
   */
  destroy(): void {
    // computing组件会在父类destroy中被销毁
    super.destroy();
  }
}
