# 基于 Cesium 的动态粒子驱动三维气象风场可视化与台风路径模拟系统

## 项目概述

本系统是一个基于 Web 的三维气象可视化平台，专门用于台风路径模拟和风场数据分析。系统集成了真实的 ERA5 再分析数据和 IBTrACS 台风路径数据，通过 GPU 加速的粒子系统实现高性能的风场可视化，并提供台风中心定位精度分析功能。

### 核心特性

- 🌍 **三维地球可视化**: 基于 Cesium 引擎的高性能三维地球渲染
- 🌪️ **动态台风路径**: 实时台风路径追踪和历史轨迹回放
- 💨 **风场粒子系统**: GPU 加速的动态粒子渲染，支持平滑插值动画
- 📊 **多层气压数据**: 支持 1000/850/500/200 hPa 四个气压层数据
- ⏱️ **时间轴控制**: 1 现实秒=1 系统小时的时间映射，支持多倍速播放
- 🎯 **精度分析**: 台风中心与风场涡度中心的偏差分析
- 📱 **响应式设计**: 适配桌面和移动设备的现代化 UI 界面

### 技术栈

- **前端框架**: Vue 3 + TypeScript + Vite
- **状态管理**: Pinia
- **样式框架**: Tailwind CSS
- **三维引擎**: Cesium
- **图表库**: ECharts
- **数据处理**: Python + xarray + pandas
- **数据源**: ERA5 再分析数据 + IBTrACS 台风数据

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- 现代浏览器（支持 WebGL 2.0）

### 安装和运行

```bash
# 克隆项目
git clone https://github.com/521km/GISProjects.git
cd GISProjects/cesium-wind-visualization

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 数据准备

系统已包含示例数据，包括：

- 利奇马台风（2019 年）
- 海贝思台风（2019 年）
- 山竹台风（2018 年）
- 飞燕台风（2018 年）

如需添加新的台风数据，请参考[数据处理指南](./data-processing/README.md)。

## 用户需求分析

### 目标用户群体

#### 1. 气象研究人员

**需求特点**:

- 需要精确的数据分析和可视化工具
- 关注台风路径预测精度和风场结构分析
- 需要多时间尺度的数据回放和对比功能
- 要求高质量的数据导出和分析报告

**核心需求**:

- 多气压层风场数据同时显示
- 台风中心定位精度分析
- 历史台风路径对比
- 数据统计和导出功能

#### 2. 教育工作者

**需求特点**:

- 需要直观易懂的可视化效果
- 关注教学演示的流畅性和互动性
- 需要简单易用的操作界面
- 要求稳定可靠的系统性能

**核心需求**:

- 清晰的三维可视化效果
- 简单的播放控制功能
- 实时的台风路径追踪
- 教学友好的界面设计

#### 3. 学生和研究生

**需求特点**:

- 学习气象学和数据可视化技术
- 需要了解台风形成和发展过程
- 关注系统的技术实现细节
- 需要实践和实验功能

**核心需求**:

- 交互式的学习体验
- 详细的技术文档
- 可定制的参数设置
- 开源代码学习

#### 4. 应急管理人员

**需求特点**:

- 需要快速获取台风信息
- 关注台风影响范围和强度
- 需要实时监控和预警功能
- 要求移动端访问支持

**核心需求**:

- 实时台风位置显示
- 风速和气压信息
- 影响区域评估
- 移动设备兼容性

### 功能需求优先级

#### 高优先级（核心功能）

1. **三维地球和台风路径显示** - 系统基础功能
2. **风场粒子动画** - 核心可视化效果
3. **时间轴控制** - 数据回放功能
4. **台风数据加载** - 数据管理功能
5. **基础 UI 控制** - 用户交互界面

#### 中优先级（增强功能）

1. **多气压层切换** - 数据分析功能
2. **粒子参数调节** - 可视化定制
3. **台风精度分析** - 专业分析工具
4. **数据统计面板** - 信息展示
5. **响应式设计** - 设备兼容性

#### 低优先级（扩展功能）

1. **数据导出功能** - 高级工具
2. **自定义数据导入** - 扩展性功能
3. **多台风对比** - 研究工具
4. **性能优化选项** - 系统调优
5. **高级分析算法** - 专业功能

### 非功能性需求

#### 性能要求

- **响应时间**: 界面操作响应时间 < 100ms
- **加载时间**: 数据加载时间 < 3 秒
- **帧率**: 动画播放帧率 ≥ 30 FPS
- **内存使用**: 浏览器内存占用 < 1GB

#### 兼容性要求

- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **设备**: 桌面电脑、笔记本电脑、平板电脑
- **分辨率**: 最小支持 1024x768，推荐 1920x1080

#### 可用性要求

- **学习成本**: 新用户 15 分钟内掌握基本操作
- **操作效率**: 熟练用户完成常用任务 < 30 秒
- **错误恢复**: 系统错误后能自动恢复或提供明确指导
- **帮助系统**: 提供完整的操作指南和常见问题解答

## 用户操作流程指南

### 界面布局说明

系统采用现代化的响应式设计，主要包含以下区域：

```
┌─────────────────────────────────────────────────────────────┐
│                    系统标题栏                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────┐                                   ┌─────────┐  │
│  │ 粒子控制 │                                   │ 数据选择 │  │
│  │ 面板    │           主要显示区域              │ 面板    │  │
│  │(左侧)   │         (Cesium 三维地球)          │(右侧)   │  │
│  └─────────┘                                   └─────────┘  │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │ 风场数据图例 │  │ 台风分析面板 │                          │
│  │(可拖拽)     │  │(可拖拽)     │                          │
│  └─────────────┘  └─────────────┘                          │
├─────────────────────────────────────────────────────────────┤
│                    时间控制面板                              │
└─────────────────────────────────────────────────────────────┘
```

### 基础操作流程

#### 1. 系统启动和初始化

**步骤 1**: 打开浏览器访问系统

- 在地址栏输入系统 URL
- 等待系统加载完成（约 3-5 秒）
- 确认看到三维地球和系统界面

**步骤 2**: 检查系统状态

- 观察右侧数据选择面板是否显示台风列表
- 确认底部时间控制面板显示"未加载风场数据"
- 检查左侧粒子控制面板是否可见

#### 2. 选择和加载台风数据

**步骤 1**: 打开数据选择面板

- 点击右侧边缘的数据选择面板（如果未展开）
- 面板会滑出显示可用的台风列表

**步骤 2**: 选择台风

- 在台风列表中选择感兴趣的台风（如"利奇马 2019"）
- 点击台风名称，系统开始加载台风路径数据
- 观察三维地球上出现台风路径线

**步骤 3**: 选择风场数据

- 在风场列表中选择时间点（如"2019-08-03 00:00"）
- 选择气压层（默认 1000 hPa）
- 点击"加载数据"按钮
- 等待风场粒子开始显示

#### 3. 风场可视化操作

**步骤 1**: 观察粒子效果

- 确认看到动态的风场粒子在地球表面流动
- 粒子颜色表示风速强度（蓝色=低风速，红色=高风速）
- 粒子密度和长度可以调节

**步骤 2**: 调整粒子参数

- 打开左侧粒子控制面板
- 调整"粒子数量"滑块改变粒子密度
- 调整"粒子速度"滑块改变动画速度
- 调整"线条长度"改变粒子轨迹长度
- 实时观察效果变化

**步骤 3**: 切换气压层

- 在右侧数据面板中选择不同气压层
- 观察不同高度的风场模式差异
- 比较 1000 hPa（近地面）和 200 hPa（高空）的风场结构

#### 4. 时间轴控制和动画播放

**步骤 1**: 启动时间播放

- 确认底部时间控制面板显示当前时间
- 点击播放按钮（▶️）开始动画
- 观察台风路径逐步显示和风场变化

**步骤 2**: 控制播放速度

- 使用速度控制按钮（0.5x, 1x, 2x, 5x）
- 1x 速度表示 1 现实秒=1 系统小时
- 观察不同速度下的动画效果

**步骤 3**: 监控播放进度

- 观察进度条显示当前播放位置
- 查看"当前时间"显示的具体时刻
- 注意播放状态指示器

#### 5. 台风路径分析

**步骤 1**: 观察台风移动

- 注意红色台风中心标记的移动
- 观察台风路径线的逐步绘制
- 查看台风信息标签（风速、气压、等级）

**步骤 2**: 启用精度分析

- 打开台风分析面板（可拖拽窗口）
- 勾选"显示中心对比"选项
- 观察台风中心与风场涡度中心的对比

**步骤 3**: 查看分析结果

- 在分析面板中查看实时偏差数据
- 观察"涡度中心偏差"和"台风眼偏差"数值
- 查看统计摘要和精度评级

### 高级操作指南

#### 1. 三维场景交互

**鼠标操作**:

- **左键拖拽**: 旋转地球视角
- **右键拖拽**: 平移视图
- **滚轮**: 缩放视图
- **双击**: 快速缩放到点击位置

**键盘快捷键**:

- **空格键**: 播放/暂停动画
- **方向键**: 微调视角
- **+/-**: 缩放控制
- **R**: 重置视角到默认位置

#### 2. 数据对比分析

**多气压层对比**:

1. 加载基础风场数据（如 1000 hPa）
2. 在数据面板中切换到其他气压层（如 500 hPa）
3. 观察不同高度的风场结构差异
4. 注意台风眼在不同高度的表现

**时间序列分析**:

1. 选择台风发展的关键时间点
2. 使用播放控制观察台风强度变化
3. 在分析面板中记录精度数据
4. 对比台风不同发展阶段的特征

#### 3. 可视化定制

**粒子效果调优**:

- **粒子数量**: 4096-16384（性能 vs 效果平衡）
- **更新频率**: 调整动画流畅度
- **颜色方案**: 选择适合的风速颜色映射
- **轨迹长度**: 根据分析需求调整

**界面布局调整**:

- 拖拽分析面板到合适位置
- 调整面板透明度和大小
- 隐藏不需要的控制面板
- 适配不同屏幕尺寸

#### 4. 性能优化建议

**系统配置**:

- 推荐使用独立显卡
- 确保浏览器硬件加速开启
- 关闭不必要的浏览器扩展
- 保持充足的系统内存

**数据加载优化**:

- 预加载常用台风数据
- 使用较低的粒子密度进行初步分析
- 避免同时加载多个大型数据集
- 定期清理浏览器缓存

### 典型使用场景

#### 场景 1: 教学演示

**目标**: 向学生展示台风结构和发展过程

**操作流程**:

1. 选择经典台风案例（如利奇马 2019）
2. 从台风形成初期开始播放
3. 使用 1x 播放速度，配合讲解
4. 重点展示台风眼、螺旋结构
5. 对比不同气压层的风场特征

**关键要点**:

- 保持播放流畅，避免卡顿
- 适时暂停讲解重要现象
- 使用缩放功能突出细节
- 结合分析面板数据说明

#### 场景 2: 科研分析

**目标**: 分析台风路径预测精度

**操作流程**:

1. 加载研究目标台风数据
2. 启用台风分析面板
3. 开启中心对比功能
4. 记录关键时间点的偏差数据
5. 导出统计结果进行进一步分析

**关键要点**:

- 关注 3 小时间隔的分析时间点
- 记录涡度中心和台风眼偏差
- 分析不同发展阶段的精度变化
- 考虑气压层对分析结果的影响

#### 场景 3: 应急响应

**目标**: 快速了解台风当前状态和趋势

**操作流程**:

1. 快速加载最新台风数据
2. 查看当前位置和强度信息
3. 观察未来几小时的移动趋势
4. 评估影响区域和风险等级

**关键要点**:

- 重点关注台风中心位置
- 注意风速和气压变化趋势
- 快速识别高风险区域
- 保持数据更新的及时性

## 常见问题解答

### 系统运行问题

#### Q1: 系统加载缓慢或无法启动

**可能原因**:

- 网络连接不稳定
- 浏览器不支持 WebGL 2.0
- 系统资源不足

**解决方案**:

1. 检查网络连接，刷新页面重试
2. 更新浏览器到最新版本
3. 启用浏览器硬件加速：
   - Chrome: 设置 → 高级 → 系统 → 使用硬件加速
   - Firefox: about:config → webgl.force-enabled = true
4. 关闭其他占用资源的程序

#### Q2: 粒子动画卡顿或帧率低

**可能原因**:

- 粒子数量设置过高
- 显卡性能不足
- 浏览器资源占用过多

**解决方案**:

1. 降低粒子数量（建议 4096-8192）
2. 关闭其他浏览器标签页
3. 降低浏览器缩放比例
4. 检查显卡驱动是否最新

#### Q3: 数据加载失败

**可能原因**:

- 数据文件缺失或损坏
- 服务器连接问题
- 浏览器缓存问题

**解决方案**:

1. 检查控制台错误信息
2. 清理浏览器缓存和 Cookie
3. 尝试加载其他台风数据
4. 检查数据文件路径是否正确

### 操作使用问题

#### Q4: 台风路径不显示

**检查步骤**:

1. 确认已选择台风数据
2. 检查台风数据是否加载成功
3. 调整视角确保台风在可视范围内
4. 查看控制台是否有错误信息

**解决方案**:

- 重新选择台风数据
- 使用"缩放到台风路径"功能
- 检查数据文件完整性

#### Q5: 时间控制面板无响应

**可能原因**:

- 风场数据未加载
- 时间数据格式错误
- JavaScript 执行错误

**解决方案**:

1. 确保先加载风场数据
2. 检查浏览器控制台错误
3. 刷新页面重新操作
4. 尝试不同的台风数据

#### Q6: 分析面板数据异常

**常见情况**:

- 偏差数值过大（>1000km）
- 分析数据不更新
- 统计摘要为空

**解决方案**:

1. 确认在 3 小时间隔时间点进行分析
2. 检查台风和风场数据的时间匹配
3. 重启分析功能
4. 验证数据质量和完整性

### 性能优化问题

#### Q7: 内存占用过高

**监控方法**:

- 使用浏览器任务管理器（Shift+Esc）
- 观察系统资源监视器

**优化建议**:

1. 定期刷新页面释放内存
2. 避免长时间连续播放
3. 降低粒子数量和质量设置
4. 关闭不必要的分析功能

#### Q8: 移动设备兼容性问题

**已知限制**:

- 移动设备 GPU 性能有限
- 触摸操作支持有限
- 小屏幕显示效果受限

**建议方案**:

1. 使用平板电脑而非手机
2. 降低粒子效果设置
3. 使用横屏模式
4. 优先使用桌面设备进行专业分析

### 数据相关问题

#### Q9: 如何添加新的台风数据

**步骤概述**:

1. 准备 ERA5 风场数据（GRIB 格式）
2. 准备 IBTrACS 台风路径数据
3. 使用数据处理脚本转换格式
4. 更新数据列表配置文件

**详细指南**: 参考[数据处理文档](./data-processing/README.md)

#### Q10: 数据精度和来源说明

**风场数据**:

- 来源: ERA5 再分析数据
- 分辨率: 0.25° × 0.25°
- 时间间隔: 3 小时
- 气压层: 1000, 850, 500, 200 hPa

**台风数据**:

- 来源: IBTrACS 数据库
- 时间分辨率: 6 小时（插值到 3 小时）
- 位置精度: ±0.1°
- 强度精度: ±5 m/s（风速）

## 技术支持

### 错误报告

如遇到系统问题，请提供以下信息：

1. 浏览器类型和版本
2. 操作系统信息
3. 具体错误现象描述
4. 浏览器控制台错误信息
5. 操作步骤重现路径

### 联系方式

- 项目仓库: [GitHub Issues](https://github.com/521km/GISProjects/issues)
- 邮箱: <EMAIL>
- 开发者: Senlin Zheng

### 版本更新

- 当前版本: v1.0.0
- 更新日志: 查看[CHANGELOG.md](./CHANGELOG.md)
- 发布计划: 定期更新和功能增强

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](./LICENSE) 文件。

## 致谢

感谢以下开源项目和数据提供方：

- [Cesium](https://cesium.com/) - 三维地球引擎
- [Vue.js](https://vuejs.org/) - 前端框架
- [ERA5](https://cds.climate.copernicus.eu/cdsapp#!/dataset/reanalysis-era5-pressure-levels) - 气象再分析数据
- [IBTrACS](https://www.ncdc.noaa.gov/ibtracs/) - 台风路径数据
- [cesium-wind-layer](https://github.com/RaymanNg/cesium-wind-layer) - 风场可视化库
