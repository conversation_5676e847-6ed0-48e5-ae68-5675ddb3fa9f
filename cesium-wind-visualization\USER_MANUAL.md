# 用户操作手册

## 快速入门指南

### 第一次使用

1. **系统要求检查**

   - 确保使用 Chrome 90+、Firefox 88+、Safari 14+或 Edge 90+浏览器
   - 确认显卡支持 WebGL 2.0
   - 建议使用 1920x1080 或更高分辨率显示器

2. **访问系统**

   - 在浏览器中打开系统 URL
   - 等待 3-5 秒完成初始化
   - 确认看到三维地球和各个控制面板

3. **基础操作测试**
   - 用鼠标拖拽旋转地球
   - 使用滚轮缩放视图
   - 检查各个面板是否正常显示

## 详细操作指南

### 1. 数据加载流程

#### 步骤 1：选择台风

1. 点击右侧"数据选择"面板
2. 在台风列表中选择目标台风（如"利奇马 2019"）
3. 观察地球上出现蓝色台风路径线
4. 台风信息会显示在路径标记上

#### 步骤 2：加载风场数据

1. 在风场数据列表中选择时间点
2. 选择气压层（推荐从 1000 hPa 开始）
3. 点击"加载数据"按钮
4. 等待风场粒子开始在地球表面流动

#### 步骤 3：验证数据加载

- 确认看到动态粒子效果
- 检查底部时间控制面板显示当前时间
- 观察台风中心红色标记是否显示

### 2. 风场可视化控制

#### 粒子参数调整

**左侧粒子控制面板**：

- **粒子数量**：4096-16384（推荐 8192）
- **粒子速度**：0.1-2.0（推荐 1.0）
- **线条长度**：10-100 像素（推荐 50）
- **更新频率**：1-10（推荐 5）

#### 颜色和视觉效果

- 蓝色：低风速（0-10 m/s）
- 绿色：中等风速（10-20 m/s）
- 黄色：较高风速（20-30 m/s）
- 橙色：高风速（30-40 m/s）
- 红色：极高风速（>40 m/s）

#### 气压层对比

1. 加载 1000 hPa 数据（近地面）
2. 切换到 850 hPa 观察差异
3. 对比 500 hPa（中层大气）
4. 查看 200 hPa（高空急流）

### 3. 时间轴控制

#### 播放控制

- **播放按钮（▶️）**：开始动画播放
- **暂停按钮（⏸️）**：暂停当前播放
- **进度条**：显示播放进度，可点击跳转

#### 速度控制

- **0.5x**：慢速播放，适合详细观察
- **1x**：标准速度（1 现实秒=1 系统小时）
- **2x**：快速播放，适合概览
- **5x**：超快速播放，适合长时间序列

#### 时间信息

- **当前时间**：显示风场数据对应的 UTC 时间
- **数据范围**：显示可用数据的时间跨度
- **播放状态**：显示当前播放状态和速度

### 4. 台风分析功能

#### 启用分析面板

1. 找到屏幕中央的"台风分析"面板
2. 如果面板折叠，点击展开按钮（▶）
3. 勾选"显示中心对比"选项
4. 开始播放观察分析结果

#### 理解分析数据

**实时分析指标**：

- **涡度中心偏差**：台风中心与风场涡度中心的距离
- **台风眼偏差**：台风中心与风速最小点的距离
- **风速**：当前台风最大风速
- **气压**：当前台风中心气压

**精度评级**：

- **优秀**：偏差 < 50km
- **良好**：偏差 50-100km
- **可接受**：偏差 100-200km
- **需改进**：偏差 > 200km

#### 统计摘要

- **平均偏差**：整个时间序列的平均偏差
- **偏差范围**：最小和最大偏差值
- **数据点数**：参与统计的有效数据点数量

### 5. 三维场景交互

#### 鼠标操作

- **左键拖拽**：旋转地球视角
- **右键拖拽**：平移视图位置
- **滚轮滚动**：缩放视图距离
- **双击**：快速缩放到点击位置

#### 键盘快捷键

- **空格键**：播放/暂停切换
- **方向键**：微调视角方向
- **+/-键**：缩放控制
- **R 键**：重置视角到默认位置

#### 视角建议

- **全球视角**：高度 8000-15000km，适合观察台风整体路径
- **区域视角**：高度 2000-5000km，适合观察台风结构
- **局部视角**：高度 500-2000km，适合观察风场细节

### 6. 界面布局管理

#### 面板控制

- **数据选择面板**：右侧滑出式，点击边缘展开/收起
- **粒子控制面板**：左侧滑出式，点击边缘展开/收起
- **分析面板**：中央可拖拽，点击标题栏拖动位置
- **图例面板**：中央可拖拽，显示风速颜色映射

#### 响应式适配

- **桌面设备**：所有功能完全可用
- **平板设备**：主要功能可用，建议横屏使用
- **手机设备**：基础功能可用，性能受限

## 常见使用场景

### 教学演示场景

**目标**：向学生展示台风结构和发展过程

**推荐流程**：

1. 选择经典台风案例（利奇马 2019）
2. 从台风形成初期开始播放
3. 使用 1x 播放速度配合讲解
4. 重点展示台风眼和螺旋结构
5. 对比不同气压层的风场特征
6. 使用分析功能说明台风定位精度

**关键要点**：

- 保持播放流畅，避免卡顿
- 适时暂停讲解重要现象
- 使用缩放功能突出细节
- 结合分析面板数据说明

### 科研分析场景

**目标**：分析台风路径预测精度

**推荐流程**：

1. 加载研究目标台风数据
2. 启用台风分析面板
3. 开启中心对比功能
4. 记录关键时间点的偏差数据
5. 分析不同发展阶段的精度变化
6. 导出统计结果进行进一步分析

**关键要点**：

- 关注 3 小时间隔的分析时间点
- 记录涡度中心和台风眼偏差
- 考虑气压层对分析结果的影响
- 注意数据质量和完整性

### 应急响应场景

**目标**：快速了解台风当前状态和趋势

**推荐流程**：

1. 快速加载最新台风数据
2. 查看当前位置和强度信息
3. 观察未来几小时的移动趋势
4. 评估影响区域和风险等级
5. 关注风速和路径变化

**关键要点**：

- 重点关注台风中心位置
- 注意风速和气压变化趋势
- 快速识别高风险区域
- 保持数据更新的及时性

## 性能优化建议

### 系统配置优化

1. **硬件要求**：

   - 推荐使用独立显卡
   - 确保 8GB 以上内存
   - 使用 SSD 硬盘提升加载速度

2. **浏览器设置**：

   - 启用硬件加速
   - 关闭不必要的扩展
   - 定期清理缓存

3. **网络环境**：
   - 确保稳定的网络连接
   - 使用有线网络优于 WiFi
   - 避免网络高峰期使用

### 使用技巧

1. **数据加载**：

   - 预加载常用台风数据
   - 避免同时加载多个大型数据集
   - 使用较低粒子密度进行初步分析

2. **性能监控**：

   - 使用浏览器任务管理器监控资源使用
   - 观察帧率和内存占用
   - 及时调整参数优化性能

3. **故障排除**：
   - 遇到问题时检查浏览器控制台
   - 尝试刷新页面重新加载
   - 降低粒子数量和质量设置

## 故障排除指南

### 常见问题及解决方案

#### 问题 1：系统无法加载或白屏

**症状**：页面显示空白或一直显示加载状态

**可能原因**：

- 浏览器不支持 WebGL 2.0
- 网络连接问题
- JavaScript 被禁用

**解决步骤**：

1. 检查浏览器版本，确保使用 Chrome 90+等支持的浏览器
2. 在浏览器地址栏输入`chrome://gpu/`检查 WebGL 支持状态
3. 刷新页面或清除浏览器缓存
4. 检查网络连接是否正常
5. 确认 JavaScript 已启用

#### 问题 2：粒子效果不显示

**症状**：地球正常显示但没有风场粒子

**可能原因**：

- 风场数据未正确加载
- 粒子参数设置过低
- GPU 性能不足

**解决步骤**：

1. 确认已选择并加载风场数据
2. 检查粒子数量设置是否过低（建议>4096）
3. 在浏览器控制台查看错误信息
4. 尝试降低其他图形质量设置
5. 重新选择数据重新加载

#### 问题 3：台风路径不显示

**症状**：风场正常但台风路径线不可见

**可能原因**：

- 台风数据加载失败
- 视角位置不在台风区域
- 台风路径被其他元素遮挡

**解决步骤**：

1. 重新选择台风数据
2. 使用鼠标调整视角到太平洋区域
3. 检查台风列表是否正确显示
4. 尝试缩放视图到合适距离
5. 查看控制台是否有数据加载错误

#### 问题 4：时间控制无响应

**症状**：点击播放按钮无反应或时间不更新

**可能原因**：

- 数据未完全加载
- 时间数据格式错误
- 浏览器性能不足

**解决步骤**：

1. 等待数据完全加载后再操作
2. 刷新页面重新加载数据
3. 检查浏览器内存使用情况
4. 尝试降低播放速度
5. 关闭其他占用资源的程序

#### 问题 5：分析面板数据异常

**症状**：偏差数值显示异常大或不更新

**可能原因**：

- 分析算法计算错误
- 数据时间不匹配
- 台风中心识别失败

**解决步骤**：

1. 确认当前时间是 3 小时间隔的整点
2. 检查台风和风场数据时间是否匹配
3. 重新启动分析功能
4. 尝试不同的台风数据
5. 查看控制台错误信息

### 性能优化指南

#### 系统配置优化

**硬件建议**：

- CPU：Intel i5 或 AMD Ryzen 5 以上
- 内存：8GB 以上（推荐 16GB）
- 显卡：支持 WebGL 2.0 的独立显卡
- 存储：SSD 硬盘提升加载速度

**浏览器设置**：

1. **启用硬件加速**：

   - Chrome：设置 → 高级 → 系统 → 使用硬件加速
   - Firefox：about:config→layers.acceleration.force-enabled=true

2. **调整内存限制**：

   - Chrome：启动参数添加--max_old_space_size=4096
   - 定期重启浏览器释放内存

3. **优化扩展程序**：
   - 禁用不必要的浏览器扩展
   - 关闭广告拦截器（可能影响 WebGL）
   - 使用隐私模式测试性能

#### 使用技巧优化

**数据加载优化**：

- 按需加载数据，避免同时加载多个台风
- 优先加载关键时间点的数据
- 使用较低气压层数据进行初步分析
- 定期清理浏览器缓存

**渲染性能优化**：

- 根据设备性能调整粒子数量
- 降低不必要的视觉效果
- 避免频繁切换气压层
- 合理设置播放速度

**内存管理**：

- 长时间使用后刷新页面
- 避免同时打开多个系统标签页
- 监控浏览器内存使用情况
- 及时关闭不需要的分析面板

### 数据质量检查

#### 数据完整性验证

**检查项目**：

1. 台风路径数据是否连续
2. 风场数据时间间隔是否正确
3. 气压层数据是否完整
4. 数据文件格式是否正确

**验证方法**：

- 查看数据列表是否显示完整
- 检查时间序列是否有缺失
- 观察粒子效果是否正常
- 验证台风路径是否合理

#### 数据精度评估

**评估指标**：

- 台风中心位置精度
- 风速数据准确性
- 时间同步精度
- 气压数据一致性

**评估方法**：

- 对比官方台风路径数据
- 检查风速分布是否合理
- 验证时间戳是否正确
- 比较不同气压层数据

### 技术支持资源

#### 在线帮助

- **用户手册**：详细的操作指南和功能说明
- **FAQ 文档**：常见问题和解决方案
- **视频教程**：操作演示和案例分析
- **技术博客**：深入的技术原理解释

#### 社区支持

- **GitHub Issues**：报告问题和功能请求
- **讨论论坛**：用户交流和经验分享
- **邮件支持**：直接联系开发团队
- **学术交流**：参与相关学术会议和研讨会

#### 开发者资源

- **源代码**：完整的开源代码库
- **API 文档**：详细的接口说明
- **开发指南**：二次开发和定制指南
- **贡献指南**：参与项目开发的流程

## 最佳实践建议

### 教学使用建议

1. **课前准备**：提前测试系统功能，准备备用方案
2. **演示技巧**：使用大屏幕投影，确保学生能清楚看到
3. **互动设计**：让学生参与操作，增强学习效果
4. **案例选择**：选择典型的台风案例进行分析
5. **时间控制**：合理安排演示时间，避免过长或过短

### 科研使用建议

1. **数据验证**：使用前验证数据质量和完整性
2. **参数记录**：详细记录分析参数和设置
3. **结果保存**：及时保存重要的分析结果
4. **对比分析**：使用多个台风案例进行对比研究
5. **文档记录**：详细记录研究过程和发现

### 系统维护建议

1. **定期更新**：保持浏览器和系统的最新版本
2. **数据备份**：定期备份重要的数据和配置
3. **性能监控**：定期检查系统性能和资源使用
4. **安全检查**：确保系统和数据的安全性
5. **用户反馈**：收集用户反馈，持续改进系统
