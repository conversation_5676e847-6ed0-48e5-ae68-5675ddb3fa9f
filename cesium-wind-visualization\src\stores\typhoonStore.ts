import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { TyphoonData, TyphoonListItem } from "@/types/typhoon";
import {
  loadTyphoonList as loadTyphoonListData,
  loadTyphoonData,
} from "@/utils/dataLoader";

export const useTyphoonStore = defineStore("typhoon", () => {
  // 状态
  const typhoons = ref<TyphoonListItem[]>([]);
  const selectedTyphoon = ref<TyphoonData | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const hasTyphoons = computed(() => typhoons.value.length > 0);
  const selectedTyphoonId = computed(() => selectedTyphoon.value?.id || null);

  // 动作
  const loadTyphoonList = async () => {
    isLoading.value = true;
    error.value = null;

    try {
      typhoons.value = await loadTyphoonListData();
    } catch (err) {
      error.value = err instanceof Error ? err.message : "加载台风列表失败";
    } finally {
      isLoading.value = false;
    }
  };

  const loadTyphoonData = async (typhoonId: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      // 加载真实的台风数据文件
      const response = await fetch(`/data/typhoons/${typhoonId}.json`);
      if (!response.ok) {
        throw new Error(`Failed to load typhoon data: ${response.status}`);
      }

      const typhoonData = await response.json();
      selectedTyphoon.value = typhoonData;

      console.log(
        `Successfully loaded typhoon data for: ${typhoonId}`,
        typhoonData
      );
    } catch (err) {
      console.error(`Failed to load typhoon data for ${typhoonId}:`, err);
      error.value = err instanceof Error ? err.message : "加载台风数据失败";
      selectedTyphoon.value = null;
    } finally {
      isLoading.value = false;
    }
  };

  const selectTyphoon = async (typhoonId: string) => {
    await loadTyphoonData(typhoonId);
  };

  const clearSelection = () => {
    selectedTyphoon.value = null;
  };

  return {
    // 状态
    typhoons,
    selectedTyphoon,
    isLoading,
    error,

    // 计算属性
    hasTyphoons,
    selectedTyphoonId,

    // 动作
    loadTyphoonList,
    loadTyphoonData,
    selectTyphoon,
    clearSelection,
  };
});
