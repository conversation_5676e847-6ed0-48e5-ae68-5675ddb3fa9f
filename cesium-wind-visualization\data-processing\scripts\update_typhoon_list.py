#!/usr/bin/env python3
"""
更新台风列表文件
从现有的台风数据文件生成完整的台风列表
"""

import os
import json
import glob
from config import PUBLIC_DATA_DIR

def update_typhoon_list():
    """更新台风列表文件"""
    print("🔄 更新台风列表...")
    
    typhoons_dir = os.path.join(PUBLIC_DATA_DIR, "typhoons")
    
    if not os.path.exists(typhoons_dir):
        print("❌ 台风数据目录不存在")
        return False
    
    # 查找所有台风数据文件
    typhoon_files = glob.glob(os.path.join(typhoons_dir, "*.json"))
    typhoon_files = [f for f in typhoon_files if not f.endswith("list.json")]
    
    print(f"📁 找到 {len(typhoon_files)} 个台风数据文件")
    
    typhoon_list = []
    
    for file_path in typhoon_files:
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 提取列表所需的信息
            typhoon_info = {
                "id": data["id"],
                "name": data["name"],
                "year": data["year"],
                "startTime": data["startTime"],
                "endTime": data["endTime"],
                "maxWindSpeed": data["maxWindSpeed"],
                "minPressure": data["minPressure"],
                "category": classify_typhoon_category(data["maxWindSpeed"])
            }
            
            typhoon_list.append(typhoon_info)
            print(f"✅ 添加台风: {data['name']} ({data['year']})")
            
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {e}")
    
    # 按年份和开始时间排序
    typhoon_list.sort(key=lambda x: (x["year"], x["startTime"]))
    
    # 保存列表文件
    list_file = os.path.join(typhoons_dir, "list.json")
    with open(list_file, "w", encoding="utf-8") as f:
        json.dump(typhoon_list, f, ensure_ascii=False, indent=2)
    
    print(f"💾 台风列表已保存: {list_file}")
    print(f"📊 总计 {len(typhoon_list)} 个台风")
    
    return True

def classify_typhoon_category(wind_speed_knots):
    """根据风速分类台风强度"""
    # 转换为m/s (1 knot = 0.514444 m/s)
    wind_speed_ms = wind_speed_knots * 0.514444

    if wind_speed_ms >= 51:
        return "超强台风"
    elif wind_speed_ms >= 41.5:
        return "强台风"
    elif wind_speed_ms >= 32.7:
        return "台风"
    elif wind_speed_ms >= 24.5:
        return "强热带风暴"
    elif wind_speed_ms >= 17.2:
        return "热带风暴"
    elif wind_speed_ms >= 10.8:
        return "热带低压"
    else:
        return "热带扰动"

def main():
    """主函数"""
    print("🚀 台风列表更新工具")
    print("=" * 50)
    
    success = update_typhoon_list()
    
    if success:
        print("\n✅ 台风列表更新完成！")
    else:
        print("\n❌ 台风列表更新失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
