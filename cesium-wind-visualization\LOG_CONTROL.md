# 日志控制说明

## 🔇 **默认静默模式**

为了减少控制台日志噪音，系统默认启用静默模式，只显示错误和警告信息。

## 🛠️ **开发调试**

如需查看详细日志进行调试，可在浏览器控制台运行以下命令：

### 启用日志
```javascript
// 方法1：使用便捷函数
enableLogs()

// 方法2：使用Logger类
Logger.restore()
```

### 禁用日志
```javascript
// 方法1：使用便捷函数
disableLogs()

// 方法2：使用Logger类
Logger.silent()
```

## 📊 **日志级别**

系统支持以下日志级别：

- **🔍 DEBUG**: 调试信息（仅开发环境）
- **ℹ️ INFO**: 一般信息（仅开发环境）
- **⚠️ WARN**: 警告信息（所有环境）
- **❌ ERROR**: 错误信息（所有环境）

## 🎯 **专用日志类型**

- **🌀 TYPHOON**: 台风分析相关
- **💨 WIND**: 风场分析相关
- **🕐 TIME**: 时间控制相关
- **✅ SUCCESS**: 成功操作
- **⏱️ PERFORMANCE**: 性能监控

## 🔧 **开发者工具**

在浏览器控制台中可用的全局方法：

```javascript
// 快速启用/禁用日志
enableLogs()   // 启用所有日志
disableLogs()  // 禁用所有日志

// 访问Logger类的所有方法
Logger.debug("调试信息")
Logger.info("一般信息")
Logger.warn("警告信息")
Logger.error("错误信息")
Logger.typhoon("台风相关信息")
Logger.wind("风场相关信息")
Logger.time("时间相关信息")
```

## 📝 **注意事项**

1. **生产环境**: 自动禁用DEBUG和INFO级别日志
2. **开发环境**: 可通过控制台命令控制日志显示
3. **错误日志**: 始终显示，无法禁用
4. **性能**: 静默模式下可显著减少控制台输出，提升性能

## 🚀 **推荐使用方式**

- **正常使用**: 保持默认静默模式
- **功能调试**: 临时启用日志 `enableLogs()`
- **性能测试**: 确保禁用日志 `disableLogs()`
- **问题排查**: 启用日志并查看相关错误信息
