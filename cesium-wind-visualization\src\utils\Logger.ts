/**
 * 日志工具类
 * 提供统一的日志管理，支持开发/生产环境区分
 */
export class Logger {
  private static isDevelopment = import.meta.env.DEV || false;
  private static isEnabled = true; // 全局日志开关

  /**
   * 启用/禁用日志
   */
  static setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 调试日志 - 仅在开发环境显示
   */
  static debug(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`🔍 ${message}`, ...args);
    }
  }

  /**
   * 信息日志 - 仅在开发环境显示
   */
  static info(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`ℹ️ ${message}`, ...args);
    }
  }

  /**
   * 警告日志 - 所有环境显示
   */
  static warn(message: string, ...args: any[]): void {
    console.warn(`⚠️ ${message}`, ...args);
  }

  /**
   * 错误日志 - 所有环境显示
   */
  static error(message: string, ...args: any[]): void {
    console.error(`❌ ${message}`, ...args);
  }

  /**
   * 成功日志 - 仅在开发环境显示
   */
  static success(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`✅ ${message}`, ...args);
    }
  }

  /**
   * 性能日志 - 仅在开发环境显示
   */
  static performance(label: string, startTime: number): void {
    if (this.isEnabled && this.isDevelopment) {
      const duration = performance.now() - startTime;
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * 台风分析专用日志
   */
  static typhoon(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`🌀 ${message}`, ...args);
    }
  }

  /**
   * 风场分析专用日志
   */
  static wind(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`💨 ${message}`, ...args);
    }
  }

  /**
   * 时间控制专用日志
   */
  static time(message: string, ...args: any[]): void {
    if (this.isEnabled && this.isDevelopment) {
      console.log(`🕐 ${message}`, ...args);
    }
  }

  /**
   * 静默模式 - 关闭所有非错误日志
   */
  static silent(): void {
    this.isEnabled = false;
  }

  /**
   * 恢复日志输出
   */
  static restore(): void {
    this.isEnabled = true;
  }
}
