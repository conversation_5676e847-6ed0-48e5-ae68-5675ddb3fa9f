<script setup lang="ts">
import { ref, provide } from "vue";
import CesiumViewer from "./components/CesiumViewer.vue";
import DataPanel from "./components/DataPanel.vue";
import ParticleControlPanel from "./components/ParticleControlPanel.vue";
import WindDataLegend from "./components/WindDataLegend.vue";
import DataDrivenTimeControl from "./components/DataDrivenTimeControl.vue";
import TyphoonAnalysisPanel from "./components/TyphoonAnalysisPanel.vue";
// 移除uiStore，改为抽拉式设计
const cesiumViewerRef = ref();
const typhoonAnalysisPanelRef = ref();

// 移除面板切换函数，改为抽拉式设计

// 移除时间控制面板显示状态，改为始终显示

// 提供CesiumViewer引用给子组件
provide("cesiumViewerRef", cesiumViewerRef);
provide("typhoonAnalysisPanelRef", typhoonAnalysisPanelRef);
</script>

<template>
  <div class="app-container">
    <!-- Cesium 主视图 -->
    <CesiumViewer ref="cesiumViewerRef" />

    <!-- 提供CesiumViewer引用给子组件 -->
    <template v-if="cesiumViewerRef">
      <div style="display: none">{{ cesiumViewerRef }}</div>
    </template>

    <!-- 数据选择面板 - 右侧抽拉式 -->
    <DataPanel />

    <!-- 粒子控制面板 - 左侧抽拉式 -->
    <ParticleControlPanel />

    <!-- 风场数据图例 - 中央顶部可拖拽 -->
    <WindDataLegend />

    <!-- 台风分析面板 - 中央顶部可拖拽 -->
    <TyphoonAnalysisPanel ref="typhoonAnalysisPanelRef" />

    <!-- 应用标题 -->
    <div class="app-title">
      <h1>基于 Cesium 的动态粒子驱动三维气象风场可视化与台风路径模拟系统</h1>
    </div>

    <!-- 数据驱动的时间控制面板 - 底部横向 -->
    <DataDrivenTimeControl />
  </div>
</template>

<style scoped>
.app-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 移除面板切换按钮样式 */

.app-title {
  position: absolute;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  max-width: 80%;
}

.app-title h1 {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 1200px) {
  .app-title h1 {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .app-title {
    display: none;
  }
}
</style>
