// 风场数据类型定义

export interface WindDataDimension {
  array: Float32Array;
  min?: number;
  max?: number;
}

export interface WindFieldData {
  u: WindDataDimension;
  v: WindDataDimension;
  speed?: WindDataDimension;
  temperature?: WindDataDimension;
  verticalVelocity?: WindDataDimension;
  width: number;
  height: number;
  bounds: {
    west: number;
    south: number;
    east: number;
    north: number;
  };
}

export interface WindDataFile {
  /** 时间戳 (UTC) */
  time: string;
  /** 气压层数据 */
  pressureLevels: {
    [key: string]: WindFieldData; // "1000", "850", "500", "200"
  };
  /** 数据边界 */
  bounds: {
    west: number;
    south: number;
    east: number;
    north: number;
  };
  /** 数据分辨率 */
  resolution: {
    width: number;
    height: number;
  };
}

export interface WindDataListItem {
  /** 文件名 */
  filename: string;
  /** 时间 */
  time: string;
  /** 日期 */
  date: string;
  /** 小时 */
  hour: string;
  /** 可用的气压层 */
  pressureLevels: string[];
  /** 文件路径 */
  path: string;
}

export interface WindDataAtPosition {
  /** 原始数据 */
  original: {
    u: number;
    v: number;
    speed: number;
    temperature?: number;
    verticalVelocity?: number;
  };
  /** 插值数据 */
  interpolated: {
    u: number;
    v: number;
    speed: number;
    temperature?: number;
    verticalVelocity?: number;
  };
  /** 风向 (度) */
  direction: number;
  /** 位置信息 */
  position: {
    longitude: number;
    latitude: number;
  };
}
