<template>
  <div ref="cesiumContainer" class="cesium-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, inject } from "vue";
import { Viewer, ClockRange, ClockStep, JulianDate, Cartesian3 } from "cesium";
import { WindLayerInterpolated } from "../../libs/cesium-wind-layer/src/index";
import { useWindStore } from "@/stores/windStore";
// import { useUIStore } from "@/stores/uiStore"; // 暂未使用
import { useTyphoonStore } from "@/stores/typhoonStore";
import { TyphoonPathManager } from "@/utils/TyphoonPathManager";
// 移除硬编码的时间控制器导入
// import { createLekimaTimeController } from "@/utils/timeControllerFactory";
// import type { MasterTimeController } from "@/utils/MasterTimeController";
// import type { DataState } from "@/types/timeline";

const cesiumContainer = ref<HTMLDivElement>();
let viewer: Viewer | null = null;
let windLayer: WindLayerInterpolated | null = null;
let typhoonPathManager: TyphoonPathManager | null = null;

const windStore = useWindStore();
// const uiStore = useUIStore(); // 暂未使用
const typhoonStore = useTyphoonStore();

// 注入分析面板引用
const typhoonAnalysisPanelRef = inject("typhoonAnalysisPanelRef");

// 初始化 Cesium 查看器
const initViewer = async () => {
  if (!cesiumContainer.value) return;

  try {
    viewer = new Viewer(cesiumContainer.value, {
      timeline: false, // 隐藏Cesium自带的时间轴
      animation: false, // 隐藏Cesium自带的动画控件
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      navigationHelpButton: false,
      creditContainer: document.createElement("div"), // 隐藏版权信息
    });

    // 设置视角中心到中国青岛
    viewer.camera.setView({
      destination: Cartesian3.fromDegrees(120.3, 36.1, 8000000.0),
    });

    // 配置时钟 - 基于数据驱动
    setupClock();

    // 设置事件监听器
    setupEventListeners();

    // 设置点击事件监听
    setupClickHandler();

    // 初始化台风路径管理器
    initializeTyphoonPathManager();

    // console.log("Cesium viewer initialized successfully");
  } catch (error) {
    console.error("Failed to initialize Cesium viewer:", error);
  }
};

// 设置事件监听器
const setupEventListeners = () => {
  if (!viewer) return;

  viewer.camera.percentageChanged = 0.01;
  viewer.camera.changed.addEventListener(updateViewerParameters);
  viewer.scene.morphComplete.addEventListener(updateViewerParameters);
  window.addEventListener("resize", updateViewerParameters);
};

// 移除事件监听器
const removeEventListeners = () => {
  if (!viewer) return;

  viewer.camera.changed.removeEventListener(updateViewerParameters);
  viewer.scene.morphComplete.removeEventListener(updateViewerParameters);
  window.removeEventListener("resize", updateViewerParameters);
};

// 更新视图参数
const updateViewerParameters = () => {
  // 这里可以添加视图参数更新逻辑
  // console.log("Viewer parameters updated");
};

// 移除了硬编码的MasterTimeController
// 现在完全基于windStore的数据状态来驱动时间显示

// 设置时钟 - 基于数据驱动
const setupClock = () => {
  if (!viewer) return;

  const clock = viewer.clock;

  // 基础时钟设置
  clock.clockRange = ClockRange.LOOP_STOP;
  clock.clockStep = ClockStep.SYSTEM_CLOCK_MULTIPLIER;
  clock.multiplier = 1; // 默认实时
  clock.shouldAnimate = false;

  // 时间范围将在数据加载时动态设置
  // console.log("时钟已初始化 - 等待数据驱动时间范围设置");
};

// 设置点击事件处理
const setupClickHandler = () => {
  if (!viewer) return;

  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    const pickedPosition = viewer!.camera.pickEllipsoid(
      event.position,
      viewer!.scene.globe.ellipsoid
    );
    if (pickedPosition) {
      // const cartographic =
      //   viewer!.scene.globe.ellipsoid.cartesianToCartographic(pickedPosition);
      // const longitude = (cartographic.longitude * 180) / Math.PI;
      // const latitude = (cartographic.latitude * 180) / Math.PI;
      // TODO: WindLayerInterpolated暂时没有getDataAtLonLat方法
      // 暂时注释掉点击获取风场数据功能
      // console.log(`点击位置: ${longitude.toFixed(2)}, ${latitude.toFixed(2)}`);
    }
  }, 1); // LEFT_CLICK
};

// 创建风场图层
const createWindLayer = () => {
  if (!viewer || !windStore.currentWindData) return;

  // 移除现有图层
  if (windLayer) {
    windLayer.destroy();
    windLayer = null;
  }

  try {
    windLayer = new WindLayerInterpolated(viewer, windStore.currentWindData, {
      ...windStore.particleOptions,
      enableInterpolation: true,
    });
    // console.log("Wind layer created successfully");
  } catch (error) {
    console.error("Failed to create wind layer:", error);
  }
};

// 更新风场图层
const updateWindLayer = () => {
  if (!windLayer || !windStore.currentWindData) return;

  try {
    windLayer.updateWindData(windStore.currentWindData);
    // console.log("Wind layer updated successfully");
  } catch (error) {
    console.error("Failed to update wind layer:", error);
  }
};

// 更新粒子参数
const updateParticleOptions = () => {
  if (!windLayer) return;

  try {
    windLayer.updateOptions(windStore.particleOptions);
    // console.log("Particle options updated successfully");
  } catch (error) {
    console.error("Failed to update particle options:", error);
  }
};

// 初始化台风路径管理器
const initializeTyphoonPathManager = () => {
  if (!viewer) return;

  typhoonPathManager = new TyphoonPathManager(viewer);
  // console.log("台风路径管理器已初始化");
};

// 更新台风路径显示
const updateTyphoonPath = (currentTime: string) => {
  if (!typhoonPathManager) return;

  typhoonPathManager.updateByTime(currentTime);
};

// 监听风场数据变化
watch(
  () => windStore.currentWindData,
  (newData) => {
    if (newData) {
      if (windLayer) {
        updateWindLayer();
      } else {
        createWindLayer();
      }
    }
  },
  { deep: true }
);

// 监听粒子参数变化
watch(
  () => windStore.particleOptions,
  () => {
    updateParticleOptions();
  },
  { deep: true }
);

// 监听台风数据变化
watch(
  () => typhoonStore.selectedTyphoon,
  (newTyphoon) => {
    if (!typhoonPathManager) return;

    typhoonPathManager.setTyphoonData(newTyphoon);
    // if (newTyphoon) {
    //   console.log(`台风路径已设置: ${newTyphoon.name}`);
    // }
  }
);

// 监听windStore数据变化，动态更新时钟
watch(
  () => windStore.selectedTime,
  (newTime) => {
    if (!viewer || !newTime) return;

    const clock = viewer.clock;
    clock.currentTime = JulianDate.fromIso8601(newTime);

    // 同步更新台风路径显示
    updateTyphoonPath(newTime);

    // console.log("时钟时间已更新:", newTime);

    // 注意：不在这里进行涡度分析，避免重复分析
    // 涡度分析只在 handleTimeUpdate 事件中进行，确保时间同步
    // console.log(`🔍 时钟时间已更新，等待事件触发分析: ${newTime}`);
  }
);

watch(
  () => windStore.availableTimes,
  (times) => {
    if (!viewer || !times || times.length === 0) return;

    const clock = viewer.clock;
    clock.startTime = JulianDate.fromIso8601(times[0]);
    clock.stopTime = JulianDate.fromIso8601(times[times.length - 1]);
    clock.currentTime = JulianDate.fromIso8601(times[0]);

    // console.log(`时钟范围已设置: ${times[0]} ~ ${times[times.length - 1]}`);
  }
);

onMounted(() => {
  initViewer();

  // 监听插值更新事件
  window.addEventListener("updateInterpolation", handleInterpolationUpdate);

  // 监听插值状态设置事件
  window.addEventListener("setInterpolationState", handleSetInterpolationState);

  // 监听时间控制器的时间更新事件
  window.addEventListener("timeUpdate", handleTimeUpdate);

  // 监听中心对比切换事件
  window.addEventListener(
    "toggleCenterComparison",
    handleToggleCenterComparison
  );
});

// 处理插值更新
const handleInterpolationUpdate = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { interpolationFactor } = customEvent.detail;

  if (windLayer && typeof windLayer.updateInterpolationFactor === "function") {
    windLayer.updateInterpolationFactor(interpolationFactor);
    // console.log(`WindLayer插值因子已更新: ${interpolationFactor.toFixed(3)}`);
  }
};

// 处理插值状态设置
const handleSetInterpolationState = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { currentData, nextData, interpolationFactor } = customEvent.detail;

  if (windLayer && typeof windLayer.setDualWindData === "function") {
    windLayer.setDualWindData(currentData, nextData, interpolationFactor);
    // console.log(
    //   `WindLayer插值状态已设置: 当前数据=${
    //     currentData ? "已加载" : "未加载"
    //   }, 下一数据=${
    //     nextData ? "已加载" : "未加载"
    //   }, 插值因子=${interpolationFactor}`
    // );
  }
};

// 处理中心对比切换事件
const handleToggleCenterComparison = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { visible } = customEvent.detail;

  if (typhoonPathManager) {
    typhoonPathManager.setCenterComparisonVisible(visible);
    // console.log(`中心对比显示已${visible ? "开启" : "关闭"}`);
  }
};

// 处理时间更新事件
const handleTimeUpdate = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { currentTime } = customEvent.detail;

  // console.log(`⏰ 时间更新: ${currentTime}`);

  if (currentTime) {
    updateTyphoonPath(currentTime);

    // 在精确时间点进行偏差分析
    if (typhoonPathManager && windStore.currentWindData) {
      // 移除过度的调试日志

      // 检查是否是数据时间点（每3小时：00:00, 03:00, 06:00, 09:00, 12:00, 15:00, 18:00, 21:00）
      const date = new Date(currentTime);
      const minutes = date.getUTCMinutes(); // 使用UTC时间
      const seconds = date.getUTCSeconds(); // 使用UTC时间
      const hours = date.getUTCHours(); // 使用UTC时间

      // 检查是否是3小时间隔的小时（放宽条件，不要求分钟秒为0）
      if (hours % 3 === 0) {
        console.log(
          `🎯 触发涡度分析: ${currentTime} (UTC ${hours}:${minutes}:${seconds})`
        );
        const analysisResult = typhoonPathManager.analyzeWindFieldAlignment(
          windStore.currentWindData
        );

        // 更新分析面板
        if ((typhoonAnalysisPanelRef as any)?.value && analysisResult) {
          (typhoonAnalysisPanelRef as any).value.updateAnalysis(analysisResult);
          // console.log("✅ 分析结果已发送到面板");
        } else {
          // console.log("❌ 无法更新分析面板:", {
          //   panelRef: !!(typhoonAnalysisPanelRef as any)?.value,
          //   analysisResult: !!analysisResult,
          // });
        }
      } else {
        // 非分析时间点，无需日志
      }
    }
  }
};

onUnmounted(() => {
  removeEventListeners();

  // 移除插值更新事件监听器
  window.removeEventListener("updateInterpolation", handleInterpolationUpdate);

  // 移除插值状态设置事件监听器
  window.removeEventListener(
    "setInterpolationState",
    handleSetInterpolationState
  );

  // 移除时间更新事件监听器
  window.removeEventListener("timeUpdate", handleTimeUpdate);

  // 移除中心对比切换事件监听器
  window.removeEventListener(
    "toggleCenterComparison",
    handleToggleCenterComparison
  );

  if (typhoonPathManager) {
    typhoonPathManager.destroy();
  }
  if (windLayer) {
    windLayer.destroy();
  }
  if (viewer) {
    viewer.destroy();
  }
});

// 暴露方法给父组件
defineExpose({
  viewer: () => viewer,
  windLayer: () => windLayer,
  zoomToWindData: () => {
    if (windLayer) {
      windLayer.zoomTo(2000);
    }
  },
});
</script>

<style scoped>
.cesium-container {
  width: 100%;
  height: 100%;
}
</style>
