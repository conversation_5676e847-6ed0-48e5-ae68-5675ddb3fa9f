"""
数据获取和处理配置文件
"""

import os
from datetime import datetime, timedelta

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
RAW_DATA_DIR = os.path.join(BASE_DIR, "raw_data")
PROCESSED_DATA_DIR = os.path.join(BASE_DIR, "processed_data")
PUBLIC_DATA_DIR = os.path.join(os.path.dirname(BASE_DIR), "public", "data")

# ERA5数据配置
ERA5_CONFIG = {
    "dataset": "reanalysis-era5-pressure-levels",
    "variables": [
        "temperature",
        "u_component_of_wind",
        "v_component_of_wind",
        "vertical_velocity",
    ],
    "pressure_levels": ["200", "500", "850", "1000"],
    "times": ["00:00", "03:00", "06:00", "09:00", "12:00", "15:00", "18:00", "21:00"],
    "data_format": "grib",
    "download_format": "unarchived",
}

# 台风数据配置
TYPHOON_CONFIG = {
    # 利奇马台风 (2019)
    "lekima_2019": {
        "name": "利奇马",
        "english_name": "LEKIMA",
        "year": 2019,
        "start_date": "2019-08-03",
        "end_date": "2019-08-14",
        "ibtracs_id": "2019206N11125",  # IBTrACS标识符
        "area": [35, 110, 15, 140],  # [north, west, south, east]
        "affected_regions": ["中国东南沿海", "日本", "韩国"],
    },
    # 山竹台风 (2018)
    "mangkhut_2018": {
        "name": "山竹",
        "english_name": "MANGKHUT",
        "year": 2018,
        "start_date": "2018-09-07",
        "end_date": "2018-09-17",
        "ibtracs_id": "2018252N11140",
        "area": [35, 105, 10, 140],
        "affected_regions": ["菲律宾", "中国南部", "越南"],
    },
    # 海贝思台风 (2019)
    "hagibis_2019": {
        "name": "海贝思",
        "english_name": "HAGIBIS",
        "year": 2019,
        "start_date": "2019-10-06",
        "end_date": "2019-10-13",
        "ibtracs_id": "2019279N11150",
        "area": [45, 130, 20, 150],
        "affected_regions": ["日本", "关岛"],
    },
    # 飞燕台风 (2018)
    "jebi_2018": {
        "name": "飞燕",
        "english_name": "JEBI",
        "year": 2018,
        "start_date": "2018-08-28",
        "end_date": "2018-09-05",
        "ibtracs_id": "2018240N11145",
        "area": [40, 125, 25, 145],
        "affected_regions": ["日本", "韩国"],
    },
}

# IBTrACS数据源配置
IBTRACS_CONFIG = {
    "base_url": "https://www.ncei.noaa.gov/data/international-best-track-archive-for-climate-stewardship-ibtracs",
    "version": "v04r00",
    "format": "netcdf",
}

# 数据处理配置
PROCESSING_CONFIG = {
    "wind_data_resolution": {"width": 200, "height": 150},
    "time_step_hours": 3,  # 3小时间隔
    "output_format": "json",
    "compression": False,
}


# 文件命名规范
def get_era5_filename(typhoon_id, date, time):
    """生成ERA5文件名"""
    return f"{typhoon_id}_{date.strftime('%Y%m%d')}_{time:02d}00.grib"


def get_windfield_filename(typhoon_id, date, time):
    """生成风场JSON文件名"""
    return f"{typhoon_id}_{date.strftime('%m%d')}_{time:02d}00.json"


def get_typhoon_filename(typhoon_id):
    """生成台风数据文件名"""
    return f"{typhoon_id}.json"


# 创建必要的目录
def ensure_directories():
    """确保所有必要的目录存在"""
    dirs = [
        RAW_DATA_DIR,
        os.path.join(RAW_DATA_DIR, "era5"),
        os.path.join(RAW_DATA_DIR, "ibtracs"),
        PROCESSED_DATA_DIR,
        os.path.join(PROCESSED_DATA_DIR, "typhoons"),
        os.path.join(PROCESSED_DATA_DIR, "windfields"),
        PUBLIC_DATA_DIR,
        os.path.join(PUBLIC_DATA_DIR, "typhoons"),
        os.path.join(PUBLIC_DATA_DIR, "windfields"),
    ]

    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)


if __name__ == "__main__":
    ensure_directories()
    print("目录结构创建完成")
