import { createApp } from "vue";
import { createPinia } from "pinia";
import "./style.css";
import App from "./App.vue";
import { Logger } from "./utils/Logger";

// 默认启用静默模式，减少控制台日志噪音
// 如需调试，可在浏览器控制台运行: Logger.restore()
Logger.silent();

// 在全局对象上暴露日志控制方法，方便调试
(window as any).Logger = Logger;
(window as any).enableLogs = () => {
  Logger.restore();
  console.log("✅ 日志已启用");
};
(window as any).disableLogs = () => {
  Logger.silent();
  console.log("🔇 日志已禁用");
};

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.mount("#app");
