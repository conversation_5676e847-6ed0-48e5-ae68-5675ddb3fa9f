#!/usr/bin/env python3
"""
IBTrACS数据探索工具
用于查看数据结构、搜索台风、验证数据质量
"""

import os
import sys
import xarray as xr
import pandas as pd
import numpy as np
from config import RAW_DATA_DIR

class IBTrACSExplorer:
    def __init__(self):
        self.ds = None
    
    def load_data(self):
        """加载IBTrACS数据"""
        print("📂 加载IBTrACS数据...")
        ibtracs_dir = os.path.join(RAW_DATA_DIR, "ibtracs")
        nc_file = os.path.join(ibtracs_dir, "IBTrACS.WP.v04r00.nc")
        
        if not os.path.exists(nc_file):
            print(f"❌ 数据文件不存在: {nc_file}")
            return False
        
        try:
            self.ds = xr.open_dataset(nc_file)
            print(f"✅ 数据加载成功")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def show_data_info(self):
        """显示数据集基本信息"""
        if not self.ds:
            print("❌ 请先加载数据")
            return
        
        print("\n📊 数据集信息:")
        print(f"   - 风暴数量: {self.ds.sizes['storm']}")
        print(f"   - 时间点数量: {self.ds.sizes['date_time']}")
        print(f"   - 数据维度: {dict(self.ds.dims)}")
        
        print("\n🔧 可用变量:")
        for var in sorted(self.ds.variables.keys()):
            if var not in ['storm', 'date_time']:
                print(f"   - {var}: {self.ds[var].dims}")
    
    def search_typhoon(self, search_term, year=None, limit=10):
        """搜索台风"""
        if not self.ds:
            print("❌ 请先加载数据")
            return
        
        print(f"\n🔍 搜索台风: '{search_term}'" + (f" ({year}年)" if year else ""))
        
        if 'name' not in self.ds.variables:
            print("❌ 数据集中没有name字段")
            return
        
        names = self.ds['name'].values
        found_storms = []
        
        for i, name_data in enumerate(names):
            try:
                if hasattr(name_data, 'tobytes'):
                    name_str = name_data.tobytes().decode('utf-8').strip('\x00')
                else:
                    name_str = str(name_data).strip()
                
                # 检查名称匹配
                if search_term.lower() in name_str.lower():
                    # 获取时间信息来确定年份
                    times = pd.to_datetime(self.ds['time'].values[i, :])
                    valid_times = times[~pd.isna(times)]
                    
                    if len(valid_times) > 0:
                        storm_year = valid_times[0].year
                        
                        # 年份过滤
                        if year is None or storm_year == year:
                            found_storms.append({
                                'index': i,
                                'name': name_str,
                                'year': storm_year,
                                'start_time': valid_times[0] if len(valid_times) > 0 else None
                            })
                            
                            if len(found_storms) >= limit:
                                break
                                
            except Exception as e:
                continue
        
        if found_storms:
            print(f"✅ 找到 {len(found_storms)} 个匹配的台风:")
            for storm in found_storms:
                print(f"   - 索引 {storm['index']}: {storm['name']} ({storm['year']}年)")
        else:
            print("❌ 未找到匹配的台风")
        
        return found_storms
    
    def show_storm_details(self, storm_index):
        """显示特定台风的详细信息"""
        if not self.ds:
            print("❌ 请先加载数据")
            return
        
        if storm_index >= self.ds.sizes['storm']:
            print(f"❌ 无效的风暴索引: {storm_index}")
            return
        
        print(f"\n🌀 台风详细信息 (索引: {storm_index}):")
        
        # 基本信息
        if 'name' in self.ds.variables:
            name_data = self.ds['name'].values[storm_index]
            if hasattr(name_data, 'tobytes'):
                name = name_data.tobytes().decode('utf-8').strip('\x00')
            else:
                name = str(name_data).strip()
            print(f"   - 名称: {name}")
        
        # 时间信息
        times = pd.to_datetime(self.ds['time'].values[storm_index, :])
        valid_times = times[~pd.isna(times)]
        if len(valid_times) > 0:
            print(f"   - 时间范围: {valid_times[0]} 到 {valid_times[-1]}")
            print(f"   - 数据点数量: {len(valid_times)}")
        
        # 位置信息
        lats = self.ds['lat'].values[storm_index, :]
        lons = self.ds['lon'].values[storm_index, :]
        valid_coords = ~(np.isnan(lats) | np.isnan(lons))
        
        if np.any(valid_coords):
            valid_lats = lats[valid_coords]
            valid_lons = lons[valid_coords]
            print(f"   - 纬度范围: {valid_lats.min():.2f}° 到 {valid_lats.max():.2f}°")
            print(f"   - 经度范围: {valid_lons.min():.2f}° 到 {valid_lons.max():.2f}°")
        
        # 强度信息
        if 'usa_wind' in self.ds.variables:
            winds = self.ds['usa_wind'].values[storm_index, :]
            valid_winds = winds[~np.isnan(winds)]
            if len(valid_winds) > 0:
                print(f"   - 最大风速: {valid_winds.max():.1f} knots")
        
        if 'usa_pres' in self.ds.variables:
            pressures = self.ds['usa_pres'].values[storm_index, :]
            valid_pressures = pressures[~np.isnan(pressures)]
            if len(valid_pressures) > 0:
                print(f"   - 最低气压: {valid_pressures.min():.1f} hPa")
    
    def list_recent_typhoons(self, year=2019, limit=20):
        """列出指定年份的台风"""
        print(f"\n📅 {year}年的台风列表:")
        storms = self.search_typhoon("", year=year, limit=limit)
        return storms
    
    def close(self):
        """关闭数据集"""
        if self.ds:
            self.ds.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="IBTrACS数据探索工具")
    parser.add_argument("--info", action="store_true", help="显示数据集信息")
    parser.add_argument("--search", type=str, help="搜索台风名称")
    parser.add_argument("--year", type=int, help="指定年份")
    parser.add_argument("--details", type=int, help="显示指定索引台风的详细信息")
    parser.add_argument("--list", type=int, help="列出指定年份的台风")
    
    args = parser.parse_args()
    
    explorer = IBTrACSExplorer()
    
    try:
        if not explorer.load_data():
            return 1
        
        if args.info:
            explorer.show_data_info()
        
        if args.search:
            explorer.search_typhoon(args.search, args.year)
        
        if args.details is not None:
            explorer.show_storm_details(args.details)
        
        if args.list:
            explorer.list_recent_typhoons(args.list)
        
        # 默认显示基本信息
        if not any([args.info, args.search, args.details, args.list]):
            explorer.show_data_info()
            explorer.list_recent_typhoons(2019, 10)
        
        return 0
        
    finally:
        explorer.close()

if __name__ == "__main__":
    exit(main())
