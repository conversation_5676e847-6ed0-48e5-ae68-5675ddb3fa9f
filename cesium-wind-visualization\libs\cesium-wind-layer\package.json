{"name": "cesium-wind-layer", "version": "0.10.0", "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"dev": "tsup src/index.ts --format cjs,esm --dts --sourcemap --watch", "build": "tsup src/index.ts --format cjs,esm --dts --sourcemap --tsconfig tsconfig.json", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "peerDependencies": {"cesium": "*"}, "devDependencies": {"tsup": "^8.0.2", "typescript": "^5.5.3"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/hongfaqiu/cesium-wind-layer.git"}, "bugs": {"url": "https://github.com/hongfaqiu/cesium-wind-layer/issues"}, "homepage": "https://github.com/hongfaqiu/cesium-wind-layer#readme", "license": "MIT"}