#!/usr/bin/env python3
"""
增强版IBTrACS台风数据处理脚本
结合了专用脚本的优点和通用脚本的灵活性
"""

import os
import sys
import json
import xarray as xr
import pandas as pd
import numpy as np
from config import RAW_DATA_DIR, PUBLIC_DATA_DIR, TYPHOON_CONFIG, get_typhoon_filename


class EnhancedIBTrACSProcessor:
    def __init__(self):
        """初始化处理器"""
        self.ds = None

    def load_data(self):
        """加载IBTrACS数据"""
        print("📂 加载IBTrACS数据...")
        ibtracs_dir = os.path.join(RAW_DATA_DIR, "ibtracs")
        nc_file = os.path.join(ibtracs_dir, "IBTrACS.WP.v04r00.nc")

        if not os.path.exists(nc_file):
            print(f"❌ 数据文件不存在: {nc_file}")
            return False

        try:
            self.ds = xr.open_dataset(nc_file)
            print(f"✅ 数据加载成功，包含 {self.ds.sizes['storm']} 个风暴")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def find_typhoon_by_name(self, typhoon_name):
        """通过名称查找台风"""
        print(f"🔍 查找台风: {typhoon_name}")

        if "name" not in self.ds.variables:
            print("❌ 数据集中没有name字段")
            return None

        names = self.ds["name"].values

        # 支持多种名称格式
        search_names = [
            typhoon_name.lower(),
            typhoon_name.upper(),
            typhoon_name.title(),
        ]

        for i, name_data in enumerate(names):
            try:
                if hasattr(name_data, "tobytes"):
                    name_str = name_data.tobytes().decode("utf-8").strip("\x00")
                else:
                    name_str = str(name_data).strip()

                # 检查名称匹配
                for search_name in search_names:
                    if search_name in name_str.lower():
                        print(f"🎯 找到台风: 索引 {i}, 名称: {name_str}")
                        return i

            except Exception as e:
                continue

        print(f"❌ 未找到台风: {typhoon_name}")
        return None

    def extract_typhoon_data(self, storm_idx, typhoon_config):
        """提取台风数据"""
        print(f"📊 提取台风数据 (索引: {storm_idx})...")

        try:
            # 提取基本数据
            lats = self.ds["lat"].values[storm_idx, :]
            lons = self.ds["lon"].values[storm_idx, :]

            # 提取风速数据
            wind_speeds = np.full_like(lats, np.nan)
            if "usa_wind" in self.ds.variables:
                wind_speeds = self.ds["usa_wind"].values[storm_idx, :]
            elif "wmo_wind" in self.ds.variables:
                wind_speeds = self.ds["wmo_wind"].values[storm_idx, :]

            # 提取气压数据
            pressures = np.full_like(lats, np.nan)
            if "usa_pres" in self.ds.variables:
                pressures = self.ds["usa_pres"].values[storm_idx, :]
            elif "wmo_pres" in self.ds.variables:
                pressures = self.ds["wmo_pres"].values[storm_idx, :]

            # 过滤有效数据
            valid_mask = ~(np.isnan(lats) | np.isnan(lons))
            valid_lats = lats[valid_mask]
            valid_lons = lons[valid_mask]
            valid_winds = wind_speeds[valid_mask]
            valid_pressures = pressures[valid_mask]

            # 生成时间序列
            start_time = pd.to_datetime(typhoon_config["start_date"])
            num_points = len(valid_lats)
            times = pd.date_range(start=start_time, periods=num_points, freq="3h")

            print(f"🕐 时间范围: {times[0]} 到 {times[-1]} (共{num_points}个点)")

            # 构建路径点
            track_points = []
            for i in range(num_points):
                wind_speed = valid_winds[i] if not np.isnan(valid_winds[i]) else 0
                pressure = (
                    valid_pressures[i] if not np.isnan(valid_pressures[i]) else 1013
                )
                category = self.classify_intensity(wind_speed)

                point = {
                    "time": times[i].isoformat() + "Z",
                    "longitude": float(valid_lons[i]),
                    "latitude": float(valid_lats[i]),
                    "maxWindSpeed": float(wind_speed),
                    "centralPressure": float(pressure),
                    "category": category,
                }
                track_points.append(point)

            return track_points

        except Exception as e:
            print(f"❌ 数据提取失败: {e}")
            return []

    def classify_intensity(self, wind_speed_knots):
        """台风强度分类"""
        wind_speed_ms = wind_speed_knots * 0.514444

        if wind_speed_ms >= 51:
            return "超强台风"
        elif wind_speed_ms >= 41.5:
            return "强台风"
        elif wind_speed_ms >= 32.7:
            return "台风"
        elif wind_speed_ms >= 24.5:
            return "强热带风暴"
        elif wind_speed_ms >= 17.2:
            return "热带风暴"
        elif wind_speed_ms >= 10.8:
            return "热带低压"
        else:
            return "热带扰动"

    def process_typhoon(self, typhoon_id):
        """处理指定台风"""
        print(f"🌀 处理台风: {typhoon_id}")
        print("=" * 50)

        # 检查配置
        if typhoon_id not in TYPHOON_CONFIG:
            print(f"❌ 未找到台风配置: {typhoon_id}")
            return False

        config = TYPHOON_CONFIG[typhoon_id]

        # 加载数据
        if not self.load_data():
            return False

        # 查找台风 - 优先使用英文名称
        english_name = config.get("english_name", config["name"])
        storm_idx = self.find_typhoon_by_name(english_name)
        if storm_idx is None:
            return False

        # 提取数据
        track_points = self.extract_typhoon_data(storm_idx, config)
        if not track_points:
            return False

        # 计算统计信息
        wind_speeds = [p["maxWindSpeed"] for p in track_points if p["maxWindSpeed"] > 0]
        pressures = [
            p["centralPressure"] for p in track_points if p["centralPressure"] < 1013
        ]

        # 构建台风数据
        typhoon_data = {
            "id": typhoon_id,
            "name": config["name"],
            "year": config["year"],
            "ibtracs_id": config.get("ibtracs_id", ""),
            "startTime": track_points[0]["time"],
            "endTime": track_points[-1]["time"],
            "maxWindSpeed": max(wind_speeds) if wind_speeds else 0,
            "minPressure": min(pressures) if pressures else 1013,
            "points": track_points,
            "affectedRegions": config.get("affected_regions", []),
            "source": "IBTrACS",
            "metadata": {
                "data_points": len(track_points),
                "processing_time": pd.Timestamp.now().isoformat(),
                "data_quality": "processed",
            },
        }

        # 保存数据
        output_dir = os.path.join(PUBLIC_DATA_DIR, "typhoons")
        os.makedirs(output_dir, exist_ok=True)

        output_file = os.path.join(output_dir, get_typhoon_filename(typhoon_id))
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(typhoon_data, f, ensure_ascii=False, indent=2)

        print(f"💾 数据已保存: {output_file}")
        print(f"📈 统计信息:")
        print(f"   - 路径点数量: {len(track_points)}")
        print(f"   - 最大风速: {typhoon_data['maxWindSpeed']:.1f} knots")
        print(f"   - 最低气压: {typhoon_data['minPressure']:.1f} hPa")
        print(
            f"   - 持续时间: {typhoon_data['startTime']} 到 {typhoon_data['endTime']}"
        )

        return True

    def close(self):
        """关闭数据集"""
        if self.ds:
            self.ds.close()


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="增强版IBTrACS台风数据处理")
    parser.add_argument("--typhoon", type=str, help="指定台风ID")
    parser.add_argument("--all", action="store_true", help="处理所有台风")

    args = parser.parse_args()

    processor = EnhancedIBTrACSProcessor()

    try:
        if args.typhoon:
            success = processor.process_typhoon(args.typhoon)
        elif args.all:
            success_count = 0
            for typhoon_id in TYPHOON_CONFIG:
                if processor.process_typhoon(typhoon_id):
                    success_count += 1
                print()  # 空行分隔
            success = success_count > 0
            print(f"✅ 成功处理 {success_count}/{len(TYPHOON_CONFIG)} 个台风")
        else:
            # 默认处理利奇马
            success = processor.process_typhoon("lekima_2019")

        return 0 if success else 1

    finally:
        processor.close()


if __name__ == "__main__":
    exit(main())
