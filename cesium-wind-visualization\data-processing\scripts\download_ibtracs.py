"""
IBTrACS数据下载脚本
从NOAA下载国际最佳路径档案数据
"""

import os
import sys
import requests
import logging
from tqdm import tqdm
from config import IBTRACS_CONFIG, RAW_DATA_DIR

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class IBTrACSDownloader:
    def __init__(self):
        """初始化IBTrACS下载器"""
        self.base_url = IBTRACS_CONFIG["base_url"]
        self.version = IBTRACS_CONFIG["version"]
        self.format = IBTRACS_CONFIG["format"]

    def download_global_data(self, year=None):
        """下载全球IBTrACS数据"""
        if year:
            # 下载指定年份数据
            filename = f"IBTrACS.WP.{self.version}.{year}.nc"
            url = f"{self.base_url}/{self.version}/access/netcdf/IBTrACS.WP.{self.version}.{year}.nc"
        else:
            # 下载完整数据集
            filename = f"IBTrACS.ALL.{self.version}.nc"
            url = f"{self.base_url}/{self.version}/access/netcdf/IBTrACS.ALL.{self.version}.nc"

        output_path = os.path.join(RAW_DATA_DIR, "ibtracs", filename)

        logger.info(f"开始下载IBTrACS数据: {filename}")
        logger.info(f"下载URL: {url}")

        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            # 获取文件大小
            total_size = int(response.headers.get("content-length", 0))

            with open(output_path, "wb") as f:
                with tqdm(
                    total=total_size, unit="B", unit_scale=True, desc=filename
                ) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))

            logger.info(f"下载完成: {output_path}")
            return True

        except requests.RequestException as e:
            logger.error(f"下载失败: {e}")
            return False
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False

    def download_western_pacific_data(self, year_range=None):
        """下载西太平洋数据"""
        # 直接下载完整的西太平洋数据集，包含所有年份
        filename = f"IBTrACS.WP.{self.version}.nc"
        url = f"{self.base_url}/{self.version}/access/netcdf/{filename}"

        output_path = os.path.join(RAW_DATA_DIR, "ibtracs", filename)

        if os.path.exists(output_path):
            logger.info(f"文件已存在，跳过下载: {filename}")
            return True

        logger.info("下载完整西太平洋IBTrACS数据...")
        logger.info(f"下载URL: {url}")

        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))

            with open(output_path, "wb") as f:
                with tqdm(
                    total=total_size, unit="B", unit_scale=True, desc=filename
                ) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))

            logger.info(f"下载完成: {output_path}")
            return True

        except requests.RequestException as e:
            logger.error(f"下载失败: {e}")
            logger.info("尝试下载全球数据集...")

            # 如果西太平洋数据下载失败，尝试下载全球数据
            return self.download_global_data()


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="下载IBTrACS台风路径数据")
    parser.add_argument("--year", type=int, help="下载指定年份数据")
    parser.add_argument(
        "--year-range",
        nargs=2,
        type=int,
        metavar=("START", "END"),
        help="下载年份范围数据",
    )
    parser.add_argument(
        "--western-pacific", action="store_true", help="下载西太平洋数据"
    )
    parser.add_argument("--all", action="store_true", help="下载全球数据")

    args = parser.parse_args()

    # 确保目录存在
    os.makedirs(os.path.join(RAW_DATA_DIR, "ibtracs"), exist_ok=True)

    downloader = IBTrACSDownloader()

    if args.year:
        success = downloader.download_global_data(args.year)
    elif args.year_range:
        success = downloader.download_western_pacific_data(tuple(args.year_range))
    elif args.western_pacific:
        success = downloader.download_western_pacific_data()
    elif args.all:
        success = downloader.download_global_data()
    else:
        # 默认下载2019年西太平洋数据（包含利奇马）
        success = downloader.download_western_pacific_data((2019, 2019))

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
