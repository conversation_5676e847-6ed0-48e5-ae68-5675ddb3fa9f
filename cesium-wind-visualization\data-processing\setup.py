"""
数据处理系统初始化脚本
"""
import os
import sys
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本: {sys.version}")
    return True

def install_dependencies():
    """安装Python依赖"""
    logger.info("安装Python依赖...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        logger.info("依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"依赖安装失败: {e}")
        return False

def setup_directories():
    """创建目录结构"""
    logger.info("创建目录结构...")
    
    from scripts.config import ensure_directories
    ensure_directories()
    logger.info("目录结构创建完成")

def check_cds_api():
    """检查CDS API配置"""
    logger.info("检查CDS API配置...")
    
    cdsapirc_path = os.path.expanduser('~/.cdsapirc')
    if not os.path.exists(cdsapirc_path):
        logger.warning("未找到CDS API配置文件")
        logger.info("请创建 ~/.cdsapirc 文件，内容如下:")
        logger.info("url: https://cds.climate.copernicus.eu/api/v2")
        logger.info("key: YOUR_API_KEY")
        logger.info("API密钥可在 https://cds.climate.copernicus.eu/api-how-to 获取")
        return False
    
    try:
        import cdsapi
        client = cdsapi.Client()
        logger.info("CDS API配置正确")
        return True
    except Exception as e:
        logger.error(f"CDS API配置错误: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始初始化数据处理系统...")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 创建目录
    setup_directories()
    
    # 检查CDS API
    cds_ok = check_cds_api()
    
    logger.info("初始化完成!")
    
    if not cds_ok:
        logger.warning("请配置CDS API密钥后再运行数据下载脚本")
    else:
        logger.info("系统已就绪，可以开始数据处理")
        logger.info("运行示例: python scripts/main.py")

if __name__ == '__main__':
    main()
