<template>
  <div class="particle-control-drawer" :class="{ expanded: isExpanded }">
    <!-- 抽拉手柄 -->
    <div class="drawer-handle" @click="toggleDrawer">
      <div class="handle-icon">⚙️</div>
      <div class="handle-text">粒子控制</div>
      <div class="handle-arrow" :class="{ rotated: isExpanded }">▶</div>
    </div>

    <!-- 面板内容 -->
    <div class="drawer-content">
      <div class="panel-header">
        <h3 class="text-lg font-semibold">风场粒子控制</h3>
      </div>

      <div class="space-y-6">
        <!-- 粒子设置 -->
        <div>
          <h4 class="text-sm font-medium mb-3 text-gray-300">粒子设置</h4>
          <div class="space-y-3">
            <!-- 粒子数量 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                粒子数量: {{ particleCount }}
              </label>
              <input
                type="range"
                :value="particleTextureSize"
                @input="updateParticleTextureSize"
                min="50"
                max="200"
                step="10"
                class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>2,500</span>
                <span>40,000</span>
              </div>
            </div>

            <!-- 粒子高度 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                粒子高度: {{ windStore.particleOptions.particleHeight }}m
              </label>
              <input
                type="range"
                :value="windStore.particleOptions.particleHeight"
                @input="updateParticleHeight"
                min="0"
                max="5000"
                step="100"
                class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>0m</span>
                <span>5000m</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 线条设置 -->
        <div>
          <h4 class="text-sm font-medium mb-3 text-gray-300">线条设置</h4>
          <div class="space-y-3">
            <!-- 线宽范围 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                线宽: {{ windStore.particleOptions.lineWidth.min }} -
                {{ windStore.particleOptions.lineWidth.max }}px
              </label>
              <div class="flex space-x-2">
                <input
                  type="range"
                  :value="windStore.particleOptions.lineWidth.min"
                  @input="updateLineWidthMin"
                  min="0.5"
                  max="5"
                  step="0.1"
                  class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
                <input
                  type="range"
                  :value="windStore.particleOptions.lineWidth.max"
                  @input="updateLineWidthMax"
                  min="1"
                  max="10"
                  step="0.1"
                  class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
              </div>
            </div>

            <!-- 线长范围 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                线长: {{ windStore.particleOptions.lineLength.min }} -
                {{ windStore.particleOptions.lineLength.max }}
              </label>
              <div class="flex space-x-2">
                <input
                  type="range"
                  :value="windStore.particleOptions.lineLength.min"
                  @input="updateLineLengthMin"
                  min="10"
                  max="50"
                  step="1"
                  class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
                <input
                  type="range"
                  :value="windStore.particleOptions.lineLength.max"
                  @input="updateLineLengthMax"
                  min="50"
                  max="200"
                  step="5"
                  class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
              </div>
            </div>
          </div>
        </div>

        <!-- 动画设置 -->
        <div>
          <h4 class="text-sm font-medium mb-3 text-gray-300">动画设置</h4>
          <div class="space-y-3">
            <!-- 速度因子 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                速度因子: {{ windStore.particleOptions.speedFactor.toFixed(1) }}
              </label>
              <input
                type="range"
                :value="windStore.particleOptions.speedFactor"
                @input="updateSpeedFactor"
                min="0.1"
                max="5"
                step="0.1"
                class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
            </div>

            <!-- 消失速率 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                消失速率:
                {{ (windStore.particleOptions.dropRate * 1000).toFixed(1) }}‰
              </label>
              <input
                type="range"
                :value="windStore.particleOptions.dropRate * 1000"
                @input="updateDropRate"
                min="1"
                max="10"
                step="0.1"
                class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
            </div>

            <!-- 消失增量 -->
            <div>
              <label class="block text-xs text-gray-400 mb-1">
                消失增量:
                {{
                  (windStore.particleOptions.dropRateBump * 1000).toFixed(1)
                }}‰
              </label>
              <input
                type="range"
                :value="windStore.particleOptions.dropRateBump * 1000"
                @input="updateDropRateBump"
                min="1"
                max="20"
                step="0.1"
                class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider" />
            </div>
          </div>
        </div>

        <!-- 颜色方案 -->
        <div>
          <h4 class="text-sm font-medium mb-3 text-gray-300">颜色方案</h4>
          <div class="grid grid-cols-2 gap-2">
            <button
              v-for="scheme in colorSchemes"
              :key="scheme.name"
              @click="applyColorScheme(scheme.colors)"
              :class="[
                'p-2 rounded text-xs transition-all duration-200',
                isCurrentScheme(scheme.colors)
                  ? 'bg-blue-600 border border-blue-400'
                  : 'bg-gray-700 hover:bg-gray-600 border border-gray-600',
              ]">
              <div class="flex items-center space-x-1 mb-1">
                <div
                  v-for="color in scheme.colors.slice(0, 3)"
                  :key="color"
                  :style="{ backgroundColor: color }"
                  class="w-3 h-3 rounded-full"></div>
              </div>
              <div>{{ scheme.name }}</div>
            </button>
          </div>
        </div>

        <!-- 其他设置 -->
        <div>
          <h4 class="text-sm font-medium mb-3 text-gray-300">其他设置</h4>
          <div class="space-y-2">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                :checked="windStore.particleOptions.flipY"
                @change="updateFlipY"
                class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500" />
              <span class="text-sm">反转 Y 轴</span>
            </label>

            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                :checked="windStore.particleOptions.dynamic"
                @change="updateDynamic"
                class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500" />
              <span class="text-sm">动态渲染</span>
            </label>
          </div>
        </div>

        <!-- 重置按钮 -->
        <div class="pt-2">
          <button
            @click="resetOptions"
            class="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded transition-colors duration-200">
            重置为默认值
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useWindStore } from "@/stores/windStore";

defineEmits<{
  toggle: [];
}>();

const windStore = useWindStore();

// 抽拉状态
const isExpanded = ref(false);

// 切换抽拉状态
const toggleDrawer = () => {
  isExpanded.value = !isExpanded.value;
};

// 计算粒子数量
const particleTextureSize = computed(
  () => windStore.particleOptions.particlesTextureSize
);
const particleCount = computed(
  () => particleTextureSize.value * particleTextureSize.value
);

// 颜色方案
const colorSchemes = [
  {
    name: "海洋蓝调",
    colors: [
      "#164B87",
      "#4682B4",
      "#87CEEB",
      "#B0E0E6",
      "#F0F8FF",
      "#FFE4B5",
      "#FFA500",
      "#FF6347",
      "#DC143C",
    ],
  },
  {
    name: "经典白色",
    colors: ["#ffffff"],
  },
  {
    name: "蓝色渐变",
    colors: ["#0066cc", "#0099ff", "#33ccff"],
  },
  {
    name: "风速彩虹",
    colors: [
      "#3288bd",
      "#66c2a5",
      "#abdda4",
      "#e6f598",
      "#fee08b",
      "#fdae61",
      "#f46d43",
      "#d53e4f",
    ],
  },
  {
    name: "温度色谱",
    colors: [
      "#313695",
      "#4575b4",
      "#74add1",
      "#abd9e9",
      "#e0f3f8",
      "#fee090",
      "#fdae61",
      "#f46d43",
      "#d73027",
      "#a50026",
    ],
  },
  {
    name: "夜光绿",
    colors: ["#00ff00", "#66ff66", "#ccffcc"],
  },
  {
    name: "火焰红",
    colors: ["#ff0000", "#ff6600", "#ffcc00"],
  },
];

// 更新函数
const updateParticleTextureSize = (event: Event) => {
  const value = parseInt((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({ particlesTextureSize: value });
};

const updateParticleHeight = (event: Event) => {
  const value = parseInt((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({ particleHeight: value });
};

const updateLineWidthMin = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({
    lineWidth: { ...windStore.particleOptions.lineWidth, min: value },
  });
};

const updateLineWidthMax = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({
    lineWidth: { ...windStore.particleOptions.lineWidth, max: value },
  });
};

const updateLineLengthMin = (event: Event) => {
  const value = parseInt((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({
    lineLength: { ...windStore.particleOptions.lineLength, min: value },
  });
};

const updateLineLengthMax = (event: Event) => {
  const value = parseInt((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({
    lineLength: { ...windStore.particleOptions.lineLength, max: value },
  });
};

const updateSpeedFactor = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value);
  windStore.updateParticleOptions({ speedFactor: value });
};

const updateDropRate = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value) / 1000;
  windStore.updateParticleOptions({ dropRate: value });
};

const updateDropRateBump = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value) / 1000;
  windStore.updateParticleOptions({ dropRateBump: value });
};

const updateFlipY = (event: Event) => {
  const value = (event.target as HTMLInputElement).checked;
  windStore.updateParticleOptions({ flipY: value });
};

const updateDynamic = (event: Event) => {
  const value = (event.target as HTMLInputElement).checked;
  windStore.updateParticleOptions({ dynamic: value });
};

const applyColorScheme = (colors: string[]) => {
  windStore.updateParticleOptions({ colors });
};

const isCurrentScheme = (colors: string[]) => {
  const current = windStore.particleOptions.colors;
  return (
    current.length === colors.length &&
    current.every((color, index) => color === colors[index])
  );
};

const resetOptions = () => {
  windStore.resetParticleOptions();
};
</script>

<style scoped>
/* 滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #1e40af;
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #1e40af;
}

/* 抽拉式面板样式 */
.particle-control-drawer {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 350px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateX(-310px);
  transition: transform 0.3s ease;
  z-index: 1000;
  color: white;
}

.particle-control-drawer.expanded {
  transform: translateX(0);
}

.drawer-handle {
  position: absolute;
  top: 50%;
  right: -40px;
  width: 40px;
  height: 120px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: none;
  border-radius: 0 8px 8px 0;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.drawer-handle:hover {
  background: rgba(0, 0, 0, 1);
  border-color: rgba(255, 255, 255, 0.3);
}

.handle-icon {
  font-size: 18px;
}

.handle-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  font-size: 12px;
  color: #9ca3af;
}

.handle-arrow {
  font-size: 12px;
  color: #9ca3af;
  transition: transform 0.3s ease;
}

.handle-arrow.rotated {
  transform: rotate(180deg);
}

.drawer-content {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 100px; /* 为底部时间控制面板留空间 */
}

.panel-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 自定义滚动条 */
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.drawer-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.drawer-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.drawer-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .particle-control-drawer {
    width: 300px;
    transform: translateX(-260px);
  }

  .drawer-content {
    padding: 15px;
  }
}
</style>
