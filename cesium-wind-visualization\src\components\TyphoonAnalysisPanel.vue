<template>
  <div
    ref="analysisPanelRef"
    class="draggable-analysis-panel"
    :style="{ left: `${x}px`, top: `${y}px` }">
    <div class="panel-header drag-handle">
      <div class="header-content">
        <h3>🌀 台风分析</h3>
        <div class="header-controls">
          <div class="drag-indicator">⋮⋮</div>
          <button @click="togglePanel" class="toggle-btn">
            {{ isExpanded ? "▼" : "▶" }}
          </button>
        </div>
      </div>
    </div>

    <div v-if="isExpanded" class="panel-content">
      <!-- 中心对比开关 -->
      <div class="control-group">
        <label class="switch">
          <input
            type="checkbox"
            v-model="showCenterComparison"
            @change="toggleCenterComparison" />
          <span class="slider">显示中心对比</span>
        </label>
      </div>

      <!-- 等待分析状态 -->
      <div v-if="!currentAnalysis" class="waiting-state">
        <h4>⏳ 等待分析数据</h4>
        <p>请启动播放，系统将在3小时间隔时间点进行台风中心分析</p>
        <div class="status-info">
          <span
            >分析时间点: 00:00, 03:00, 06:00, 09:00, 12:00, 15:00, 18:00, 21:00
            (UTC)</span
          >
        </div>
      </div>

      <!-- 当前分析结果 -->
      <div v-if="currentAnalysis" class="analysis-result">
        <h4>📊 当前时刻分析</h4>
        <div class="metric-grid">
          <div class="metric-item">
            <span class="metric-label">涡度中心偏差</span>
            <span
              class="metric-value"
              :class="getAccuracyClass(currentAnalysis.vorticityDistance)">
              {{ currentAnalysis.vorticityDistance.toFixed(1) }}km
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">台风眼偏差</span>
            <span
              class="metric-value"
              :class="getAccuracyClass(currentAnalysis.eyeDistance)">
              {{ currentAnalysis.eyeDistance.toFixed(1) }}km
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">风速</span>
            <span class="metric-value">
              {{ currentAnalysis.windSpeed.toFixed(1) }}m/s
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">气压</span>
            <span class="metric-value">
              {{ currentAnalysis.pressure.toFixed(0) }}hPa
            </span>
          </div>
        </div>
      </div>

      <!-- 统计摘要 -->
      <div
        v-if="
          statsSummary &&
          (statsSummary.vorticity.count > 0 || statsSummary.eye.count > 0)
        "
        class="stats-summary">
        <h4>📈 统计摘要</h4>
        <div class="stats-grid">
          <div class="stats-item">
            <div class="stats-title">涡度中心偏差</div>
            <div class="stats-values">
              <span>平均: {{ statsSummary.vorticity.avg.toFixed(1) }}km</span>
              <span
                >范围: {{ statsSummary.vorticity.min.toFixed(1) }}-{{
                  statsSummary.vorticity.max.toFixed(1)
                }}km</span
              >
            </div>
          </div>
          <div class="stats-item">
            <div class="stats-title">台风眼偏差</div>
            <div class="stats-values">
              <span>平均: {{ statsSummary.eye.avg.toFixed(1) }}km</span>
              <span
                >范围: {{ statsSummary.eye.min.toFixed(1) }}-{{
                  statsSummary.eye.max.toFixed(1)
                }}km</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 精度评级 -->
      <div
        v-if="
          statsSummary &&
          (statsSummary.vorticity.count > 0 || statsSummary.eye.count > 0)
        "
        class="accuracy-rating">
        <h4>🎯 定位精度评级</h4>
        <div class="rating-item">
          <span class="rating-label">涡度中心:</span>
          <span
            class="rating-badge"
            :class="getAccuracyClass(statsSummary?.vorticity.avg || 0)">
            {{ getAccuracyRating(statsSummary?.vorticity.avg || 0) }}
          </span>
        </div>
        <div class="rating-item">
          <span class="rating-label">台风眼:</span>
          <span
            class="rating-badge"
            :class="getAccuracyClass(statsSummary?.eye.avg || 0)">
            {{ getAccuracyRating(statsSummary?.eye.avg || 0) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import {
  TyphoonCenterComparison,
  TyphoonAccuracyStats,
} from "@/utils/TyphoonCenterComparison";
import useDraggable from "@/composables/useDraggable";

// 响应式数据
const isExpanded = ref(true);
const showCenterComparison = ref(false);
const currentAnalysis = ref<any>(null);
const accuracyStats = new TyphoonAccuracyStats();

const analysisPanelRef = ref<HTMLElement | null>(null);

// 拖拽功能
const { x, y } = useDraggable(analysisPanelRef, {
  initialX: window.innerWidth / 2 - 160, // 居中显示，稍微偏左
  initialY: 80, // 在图例面板下方
  handle: ".drag-handle",
  containment: true,
});

// 计算属性
const statsSummary = computed(() => {
  return accuracyStats.getSummary();
});

// 方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value;
};

const toggleCenterComparison = () => {
  // 通过事件通知父组件
  window.dispatchEvent(
    new CustomEvent("toggleCenterComparison", {
      detail: { visible: showCenterComparison.value },
    })
  );
  console.log("切换中心对比显示:", showCenterComparison.value);
};

const getAccuracyClass = (distance: number): string => {
  if (distance < 50) return "excellent";
  if (distance < 100) return "good";
  if (distance < 200) return "acceptable";
  return "poor";
};

const getAccuracyRating = (distance: number): string => {
  if (distance < 50) return "优秀";
  if (distance < 100) return "良好";
  if (distance < 200) return "可接受";
  return "需改进";
};

// 更新当前分析结果
const updateAnalysis = (analysis: any) => {
  console.log("📊 TyphoonAnalysisPanel 收到分析数据:", analysis);

  currentAnalysis.value = analysis;

  // 检查数据有效性：距离必须大于0且小于1000km
  const isValidData =
    analysis.vorticityDistance > 0 &&
    analysis.eyeDistance > 0 &&
    analysis.vorticityDistance < 1000 &&
    analysis.eyeDistance < 1000;

  if (isValidData) {
    // console.log(
    //   `📊 添加统计数据: 涡度=${analysis.vorticityDistance.toFixed(
    //     1
    //   )}km, 台风眼=${analysis.eyeDistance.toFixed(1)}km`
    // );
    accuracyStats.addStats(
      analysis.timestamp,
      analysis.vorticityDistance,
      analysis.eyeDistance
    );

    // 立即检查统计摘要
    const summary = accuracyStats.getSummary();
    // console.log("📊 当前统计摘要:", summary);
  } else {
    const reason =
      analysis.vorticityDistance <= 0
        ? "涡度距离无效"
        : analysis.eyeDistance <= 0
        ? "台风眼距离无效"
        : analysis.vorticityDistance >= 1000
        ? "涡度距离过大"
        : "台风眼距离过大";

    // console.warn(
    //   `📊 分析数据无效，跳过统计: 涡度=${analysis.vorticityDistance}km, 台风眼=${analysis.eyeDistance}km, 原因=${reason}`
    // );
  }
};

// 暴露方法给父组件
defineExpose({
  updateAnalysis,
  clearStats: () => accuracyStats.clear(),
});
</script>

<style scoped>
/* 可拖拽台风分析面板样式 */
.draggable-analysis-panel {
  position: fixed;
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-family: "Arial", sans-serif;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: box-shadow 0.2s ease;
}

.draggable-analysis-panel:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: grab;
  user-select: none;
}

.panel-header:active {
  cursor: grabbing;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-indicator {
  color: #9ca3af;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 2px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.panel-header:hover .drag-indicator {
  opacity: 1;
}

.toggle-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.panel-content {
  padding: 16px;
}

.control-group {
  margin-bottom: 16px;
}

.switch {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.switch input {
  margin-right: 8px;
}

.slider {
  font-size: 14px;
}

.waiting-state {
  margin-bottom: 20px;
  text-align: center;
}

.waiting-state h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ffc107;
}

.waiting-state p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #ccc;
  line-height: 1.4;
}

.status-info {
  padding: 8px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ffc107;
}

.status-info span {
  font-size: 11px;
  color: #ffc107;
}

.analysis-result {
  margin-bottom: 20px;
}

.analysis-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #4caf50;
}

.metric-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.metric-label {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: bold;
}

.metric-value.excellent {
  color: #4caf50;
}

.metric-value.good {
  color: #8bc34a;
}

.metric-value.acceptable {
  color: #ff9800;
}

.metric-value.poor {
  color: #f44336;
}

.stats-summary {
  margin-bottom: 20px;
}

.stats-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #2196f3;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-item {
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.stats-title {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 4px;
}

.stats-values {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stats-values span {
  font-size: 12px;
}

.accuracy-rating h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ff9800;
}

.rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rating-label {
  font-size: 12px;
}

.rating-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.rating-badge.excellent {
  background: #4caf50;
  color: white;
}

.rating-badge.good {
  background: #8bc34a;
  color: white;
}

.rating-badge.acceptable {
  background: #ff9800;
  color: white;
}

.rating-badge.poor {
  background: #f44336;
  color: white;
}
</style>
