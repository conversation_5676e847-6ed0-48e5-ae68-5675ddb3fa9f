# 真实数据获取和处理系统

本系统从官方数据源获取和处理真实的气象数据，包括 ERA5 风场数据和 IBTrACS 台风路径数据。

## 目录结构

```
data-processing/
├── scripts/
│   ├── main.py                   # 主处理脚本
│   ├── config.py                 # 配置文件
│   ├── download_era5.py          # ERA5风场数据下载
│   ├── download_ibtracs.py       # IBTrACS台风数据下载
│   ├── process_era5.py           # ERA5数据处理
│   ├── process_ibtracs.py        # 增强版IBTrACS数据处理
│   ├── explore_ibtracs.py        # IBTrACS数据探索工具
│   ├── update_typhoon_list.py    # 台风列表更新工具
│   └── IBTRACS_IMPROVEMENTS.md   # IBTrACS处理改进文档
├── raw_data/
│   ├── era5/                     # ERA5原始GRIB文件
│   └── ibtracs/                  # IBTrACS原始NetCDF文件
└── requirements.txt              # Python依赖
```

## 数据源

1. **ERA5 数据**: Copernicus Climate Data Store (CDS)

   - 变量: U/V 风分量、温度、垂直速度
   - 气压层: 1000, 850, 500, 200 hPa
   - 时间间隔: 3 小时
   - 格式: GRIB

2. **IBTrACS 数据**: NOAA 国际最佳路径档案
   - 台风路径、强度、时间信息
   - 格式: NetCDF

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 配置CDS API密钥 (需要注册CDS账户)
# 在 ~/.cdsapirc 文件中添加:
# url: https://cds.climate.copernicus.eu/api/v2
# key: YOUR_API_KEY
```

### 2. 运行数据处理

```bash
cd scripts

# 完整流程 (下载+处理)
python main.py

# 仅下载数据
python main.py --download-only

# 仅处理数据 (需要先有原始数据)
python main.py --process-only

# 处理指定台风
python main.py --typhoon lekima_2019
```

### 3. 单独运行脚本

#### 数据下载

```bash
# 下载IBTrACS数据
python download_ibtracs.py --western-pacific

# 下载ERA5数据
python download_era5.py --typhoon lekima_2019
```

#### 台风数据处理

```bash
# 处理台风路径数据（推荐使用增强版）
python process_ibtracs.py --typhoon lekima_2019
python process_ibtracs.py --all

# 更新台风列表文件
python update_typhoon_list.py
```

#### 数据探索工具

```bash
# 探索IBTrACS数据结构
python explore_ibtracs.py --info

# 搜索台风
python explore_ibtracs.py --search "LEKIMA"
python explore_ibtracs.py --search "台风名称" --year 2019

# 查看特定台风详情
python explore_ibtracs.py --details 3617

# 列出某年的台风
python explore_ibtracs.py --list 2019
```

#### 风场数据处理

```bash
# 处理风场数据
python process_era5.py --typhoon lekima_2019
```

## 当前可用台风数据

系统目前包含以下台风的完整数据：

| 台风 ID         | 中文名 | 英文名   | 年份 | 时间范围       | 影响区域                 |
| --------------- | ------ | -------- | ---- | -------------- | ------------------------ |
| `lekima_2019`   | 利奇马 | LEKIMA   | 2019 | 08-03 至 08-14 | 中国东南沿海、日本、韩国 |
| `mangkhut_2018` | 山竹   | MANGKHUT | 2018 | 09-07 至 09-17 | 菲律宾、中国南部、越南   |
| `hagibis_2019`  | 海贝思 | HAGIBIS  | 2019 | 10-06 至 10-13 | 日本、关岛               |
| `jebi_2018`     | 飞燕   | JEBI     | 2018 | 08-28 至 09-05 | 日本、韩国               |

### 添加新台风

要添加新台风，请在 `config.py` 的 `TYPHOON_CONFIG` 中添加配置：

```python
"new_typhoon_2020": {
    "name": "中文名",
    "english_name": "ENGLISH_NAME",  # IBTrACS中的英文名称
    "year": 2020,
    "start_date": "2020-XX-XX",
    "end_date": "2020-XX-XX",
    "ibtracs_id": "2020XXXNXXXXX",  # 可选：IBTrACS标识符
    "area": [north, west, south, east],  # 地理范围
    "affected_regions": ["地区1", "地区2"]
}
```

然后运行：

```bash
python process_ibtracs.py --typhoon new_typhoon_2020
python update_typhoon_list.py
```

## 输出数据格式

### 台风数据 (JSON)

```json
{
  "id": "lekima_2019",
  "name": "利奇马",
  "year": 2019,
  "ibtracs_id": "2019206N11125",
  "startTime": "2019-08-03T00:00:00Z",
  "endTime": "2019-08-12T06:00:00Z",
  "maxWindSpeed": 95.0,
  "minPressure": 949.0,
  "affectedRegions": ["中国东南沿海", "日本", "韩国"],
  "source": "IBTrACS",
  "metadata": {
    "data_points": 75,
    "processing_time": "2024-01-01T12:00:00",
    "data_quality": "processed"
  },
  "points": [
    {
      "time": "2019-08-03T00:00:00Z",
      "longitude": 125.8,
      "latitude": 21.0,
      "maxWindSpeed": 35.0,
      "centralPressure": 995.0,
      "category": "热带风暴"
    }
  ]
}
```

### 风场数据 (JSON)

```json
{
  "bbox": [110, 15, 140, 35],
  "width": 200,
  "height": 150,
  "unit": "m s-1",
  "time": 1564790400000,
  "pressure_level": 1000,
  "u": {
    "array": [...],
    "min": -15.2,
    "max": 18.7
  },
  "v": {
    "array": [...],
    "min": -12.3,
    "max": 16.4
  }
}
```

## 脚本功能详解

### 核心脚本

| 脚本名称                 | 功能描述                         | 推荐使用场景             |
| ------------------------ | -------------------------------- | ------------------------ |
| `main.py`                | 主处理脚本，协调所有数据处理流程 | 完整的数据处理流程       |
| `process_ibtracs.py`     | 增强版 IBTrACS 台风数据处理      | 台风路径数据获取（推荐） |
| `explore_ibtracs.py`     | IBTrACS 数据探索和调试工具       | 数据探索、台风搜索、调试 |
| `update_typhoon_list.py` | 台风列表文件更新工具             | 更新前端台风选择列表     |

### 下载脚本

| 脚本名称              | 功能描述                  | 数据源         |
| --------------------- | ------------------------- | -------------- |
| `download_ibtracs.py` | 下载 IBTrACS 台风路径数据 | NOAA IBTrACS   |
| `download_era5.py`    | 下载 ERA5 风场数据        | Copernicus CDS |

### 处理脚本

| 脚本名称             | 功能描述              | 输入格式 | 输出格式 |
| -------------------- | --------------------- | -------- | -------- |
| `process_era5.py`    | 处理 ERA5 风场数据    | GRIB     | JSON     |
| `process_ibtracs.py` | 处理 IBTrACS 台风数据 | NetCDF   | JSON     |

### 工作流程

```mermaid
graph TD
    A[配置台风信息] --> B[下载IBTrACS数据]
    A --> C[下载ERA5数据]
    B --> D[处理台风路径数据]
    C --> E[处理风场数据]
    D --> F[更新台风列表]
    E --> G[生成最终数据]
    F --> G
    G --> H[前端可视化]
```

## 配置说明

在 `config.py` 中可以配置:

- **台风信息**: 名称、时间范围、区域、IBTrACS ID
- **ERA5 下载参数**: 变量、气压层、时间间隔
- **数据处理参数**: 分辨率、输出格式、压缩选项
- **文件路径和命名规范**: 目录结构、文件命名模式

## 数据质量与验证

### 台风数据质量指标

系统会自动验证台风数据的质量：

- ✅ **数据完整性**: 路径点数量 ≥ 5
- ✅ **时间连续性**: 最大间隔 ≤ 24 小时
- ✅ **地理合理性**: 位置在西北太平洋区域
- ✅ **数据一致性**: 风速和气压数据合理
- ✅ **强度分类**: 自动根据风速分类台风强度

### 台风强度分类标准

| 分类       | 风速范围 (m/s) | 风速范围 (knots) |
| ---------- | -------------- | ---------------- |
| 热带扰动   | < 10.8         | < 21             |
| 热带低压   | 10.8-17.1      | 21-33            |
| 热带风暴   | 17.2-24.4      | 34-47            |
| 强热带风暴 | 24.5-32.6      | 48-63            |
| 台风       | 32.7-41.4      | 64-80            |
| 强台风     | 41.5-50.9      | 81-99            |
| 超强台风   | ≥ 51.0         | ≥ 100            |

### 数据验证日志示例

```
🔍 查找台风: LEKIMA
🎯 找到台风: 索引 3617, 名称: LEKIMA
📊 提取台风数据 (索引: 3617)...
🕐 时间范围: 2019-08-03 00:00:00 到 2019-08-12 06:00:00 (共75个点)
📈 统计信息:
   - 路径点数量: 75
   - 最大风速: 95.0 knots
   - 最低气压: 949.0 hPa
   - 持续时间: 2019-08-03T00:00:00Z 到 2019-08-12T06:00:00Z
✅ 数据验证完成
```

## 注意事项

1. **CDS API 配置**: 需要注册 CDS 账户并配置 API 密钥
2. **数据大小**: ERA5 数据文件较大，确保有足够存储空间
3. **下载时间**: 首次下载可能需要较长时间
4. **网络连接**: 需要稳定的网络连接
5. **台风名称**: 使用 IBTrACS 中的英文名称进行匹配

## 故障排除

### 常见问题

1. **CDS API 错误**: 检查 API 密钥配置

   ```bash
   # 检查 ~/.cdsapirc 文件是否存在且配置正确
   cat ~/.cdsapirc
   ```

2. **依赖缺失**: 运行 `pip install -r requirements.txt`

   ```bash
   pip install xarray pandas numpy netcdf4 requests tqdm
   ```

3. **台风数据未找到**:

   - 使用 `explore_ibtracs.py` 搜索正确的台风名称
   - 确认英文名称与 IBTrACS 数据库一致
   - 检查年份是否正确

4. **数据文件损坏**: 删除原始文件重新下载

   ```bash
   rm raw_data/ibtracs/*.nc
   python download_ibtracs.py --western-pacific
   ```

5. **内存不足**: 处理大文件时可能需要更多内存
   - 关闭其他应用程序
   - 考虑分批处理数据

### 调试工具

```bash
# 检查IBTrACS数据结构
python explore_ibtracs.py --info

# 搜索特定台风
python explore_ibtracs.py --search "台风名称"

# 查看处理日志
python process_ibtracs.py --typhoon lekima_2019 > debug.log 2>&1
```

### 获取帮助

如果遇到问题，请：

1. 查看详细的错误日志
2. 使用探索工具验证数据
3. 检查配置文件设置
4. 参考 `IBTRACS_IMPROVEMENTS.md` 文档
