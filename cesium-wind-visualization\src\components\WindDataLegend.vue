<template>
  <div
    ref="legendPanelRef"
    class="draggable-legend-panel"
    :style="{ left: `${x}px`, top: `${y}px` }">
    <div class="panel-header drag-handle">
      <div class="header-content">
        <h3 class="text-lg font-semibold">📈 风场数据图例</h3>
        <div class="drag-indicator">⋮⋮</div>
      </div>
    </div>

    <!-- 当前选中位置的风场信息 -->
    <div
      v-if="uiStore.selectedWindInfo"
      class="mb-4 p-3 bg-gray-700 rounded-lg">
      <h4 class="text-sm font-medium mb-2 text-gray-300">选中位置信息</h4>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span class="text-gray-400">经度:</span>
          <span class="ml-1"
            >{{ uiStore.selectedWindInfo.longitude.toFixed(3) }}°</span
          >
        </div>
        <div>
          <span class="text-gray-400">纬度:</span>
          <span class="ml-1"
            >{{ uiStore.selectedWindInfo.latitude.toFixed(3) }}°</span
          >
        </div>
        <div>
          <span class="text-gray-400">风速:</span>
          <span class="ml-1"
            >{{ uiStore.selectedWindInfo.windSpeed.toFixed(2) }} m/s</span
          >
        </div>
        <div>
          <span class="text-gray-400">风向:</span>
          <span class="ml-1"
            >{{ uiStore.selectedWindInfo.windDirection.toFixed(1) }}°</span
          >
        </div>
        <div>
          <span class="text-gray-400">气压层:</span>
          <span class="ml-1">{{ uiStore.selectedWindInfo.pressure }} hPa</span>
        </div>
        <div v-if="uiStore.selectedWindInfo.temperature">
          <span class="text-gray-400">温度:</span>
          <span class="ml-1"
            >{{ uiStore.selectedWindInfo.temperature.toFixed(1) }}°C</span
          >
        </div>
      </div>
    </div>

    <!-- 当前数据信息 -->
    <div v-if="windStore.currentWindData" class="mb-4">
      <h4 class="text-sm font-medium mb-2 text-gray-300">当前数据信息</h4>
      <div class="text-sm space-y-1">
        <div>
          <span class="text-gray-400">时间:</span>
          <span class="ml-1">{{ formatTime(windStore.selectedTime) }}</span>
        </div>
        <div>
          <span class="text-gray-400">气压层:</span>
          <span class="ml-1">{{ windStore.selectedPressureLevel }} hPa</span>
        </div>
        <div>
          <span class="text-gray-400">数据范围:</span>
          <span class="ml-1">{{
            formatBounds(windStore.currentWindData.bounds)
          }}</span>
        </div>
        <div>
          <span class="text-gray-400">分辨率:</span>
          <span class="ml-1"
            >{{ windStore.currentWindData.width }} ×
            {{ windStore.currentWindData.height }}</span
          >
        </div>
      </div>
    </div>

    <!-- 风速图例 -->
    <div class="mb-4">
      <h4 class="text-sm font-medium mb-2 text-gray-300">风速图例</h4>
      <div ref="speedLegendChart" class="h-32 w-full"></div>
    </div>

    <!-- 颜色图例 -->
    <div>
      <h4 class="text-sm font-medium mb-2 text-gray-300">颜色方案</h4>
      <div class="flex items-center space-x-1 mb-2">
        <div
          v-for="(color, index) in windStore.particleOptions.colors"
          :key="index"
          :style="{ backgroundColor: color }"
          class="flex-1 h-4 first:rounded-l last:rounded-r"></div>
      </div>
      <div class="flex justify-between text-xs text-gray-400">
        <span>低风速</span>
        <span>高风速</span>
      </div>
    </div>

    <!-- 统计信息 -->
    <div v-if="windStats" class="mt-4 p-3 bg-gray-700 rounded-lg">
      <h4 class="text-sm font-medium mb-2 text-gray-300">统计信息</h4>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span class="text-gray-400">最大风速:</span>
          <span class="ml-1">{{ windStats.maxSpeed.toFixed(2) }} m/s</span>
        </div>
        <div>
          <span class="text-gray-400">平均风速:</span>
          <span class="ml-1">{{ windStats.avgSpeed.toFixed(2) }} m/s</span>
        </div>
        <div>
          <span class="text-gray-400">最小风速:</span>
          <span class="ml-1">{{ windStats.minSpeed.toFixed(2) }} m/s</span>
        </div>
        <div>
          <span class="text-gray-400">数据点数:</span>
          <span class="ml-1">{{ windStats.totalPoints.toLocaleString() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import * as echarts from "echarts";
import { useWindStore } from "@/stores/windStore";
import { useUIStore } from "@/stores/uiStore";
import { formatTimeDisplay } from "@/utils/timeUtils";
import useDraggable from "@/composables/useDraggable";

defineEmits<{
  toggle: [];
}>();

const windStore = useWindStore();
const uiStore = useUIStore();

const speedLegendChart = ref<HTMLDivElement>();
const legendPanelRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 拖拽功能
const { x, y } = useDraggable(legendPanelRef, {
  initialX: window.innerWidth / 2 - 160, // 居中显示
  initialY: 20, // 顶部
  handle: ".drag-handle",
  containment: true,
});

// 计算风场统计信息
const windStats = computed(() => {
  if (!windStore.currentWindData) return null;

  const windData = windStore.currentWindData;

  // 优先使用已计算的speed字段
  if (
    windData.speed &&
    windData.speed.min !== undefined &&
    windData.speed.max !== undefined
  ) {
    // 如果有预计算的speed字段，直接使用
    const { u, v } = windData;
    let totalSpeed = 0;
    let validPoints = 0;

    // 只需要计算平均值，min/max已经有了
    for (let i = 0; i < u.array.length; i++) {
      const speed = Math.sqrt(
        u.array[i] * u.array[i] + v.array[i] * v.array[i]
      );
      if (!isNaN(speed) && isFinite(speed)) {
        totalSpeed += speed;
        validPoints++;
      }
    }

    return {
      maxSpeed: windData.speed.max,
      minSpeed: windData.speed.min,
      avgSpeed: validPoints > 0 ? totalSpeed / validPoints : 0,
      totalPoints: validPoints,
    };
  } else {
    // 后备方案：实时计算（兼容旧数据格式）
    const { u, v } = windData;
    let maxSpeed = 0;
    let minSpeed = Infinity;
    let totalSpeed = 0;
    let validPoints = 0;

    for (let i = 0; i < u.array.length; i++) {
      const speed = Math.sqrt(
        u.array[i] * u.array[i] + v.array[i] * v.array[i]
      );
      if (!isNaN(speed) && isFinite(speed)) {
        maxSpeed = Math.max(maxSpeed, speed);
        minSpeed = Math.min(minSpeed, speed);
        totalSpeed += speed;
        validPoints++;
      }
    }

    return {
      maxSpeed,
      minSpeed: minSpeed === Infinity ? 0 : minSpeed,
      avgSpeed: validPoints > 0 ? totalSpeed / validPoints : 0,
      totalPoints: validPoints,
    };
  }
});

// 格式化时间
const formatTime = (timeString: string) => {
  if (!timeString) return "";
  return formatTimeDisplay(timeString);
};

// 格式化边界
const formatBounds = (bounds: any) => {
  if (!bounds) return "";
  return `${bounds.west.toFixed(1)}°E - ${bounds.east.toFixed(
    1
  )}°E, ${bounds.south.toFixed(1)}°N - ${bounds.north.toFixed(1)}°N`;
};

// 初始化图表
const initChart = () => {
  if (!speedLegendChart.value) return;

  chartInstance = echarts.init(speedLegendChart.value);
  updateChart();
};

// 更新图表
const updateChart = () => {
  if (!chartInstance || !windStats.value) return;

  const option = {
    backgroundColor: "transparent",
    grid: {
      left: "10%",
      right: "10%",
      top: "10%",
      bottom: "20%",
    },
    xAxis: {
      type: "category",
      data: ["0-5", "5-10", "10-15", "15-20", "20-25", "25+"],
      axisLabel: {
        color: "#9ca3af",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#4b5563",
        },
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#9ca3af",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#4b5563",
        },
      },
      splitLine: {
        lineStyle: {
          color: "#374151",
        },
      },
    },
    series: [
      {
        name: "风速分布",
        type: "bar",
        data: calculateSpeedDistribution(),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#3b82f6" },
            { offset: 1, color: "#1e40af" },
          ]),
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#4b5563",
      textStyle: {
        color: "#ffffff",
      },
    },
  };

  chartInstance.setOption(option);
};

// 计算风速分布
const calculateSpeedDistribution = () => {
  if (!windStore.currentWindData) return [0, 0, 0, 0, 0, 0];

  const { u, v } = windStore.currentWindData;
  const distribution = [0, 0, 0, 0, 0, 0]; // 0-5, 5-10, 10-15, 15-20, 20-25, 25+

  for (let i = 0; i < u.array.length; i++) {
    const speed = Math.sqrt(u.array[i] * u.array[i] + v.array[i] * v.array[i]);
    if (!isNaN(speed) && isFinite(speed)) {
      if (speed < 5) distribution[0]++;
      else if (speed < 10) distribution[1]++;
      else if (speed < 15) distribution[2]++;
      else if (speed < 20) distribution[3]++;
      else if (speed < 25) distribution[4]++;
      else distribution[5]++;
    }
  }

  return distribution;
};

// 监听数据变化
watch(
  () => windStore.currentWindData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener("resize", handleResize);
  });
});

// 清理
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleResize);
};

// 组件卸载时清理
import { onUnmounted } from "vue";
onUnmounted(cleanup);
</script>

<style scoped>
/* 可拖拽图例面板样式 */
.draggable-legend-panel {
  position: fixed;
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: box-shadow 0.2s ease;
}

.draggable-legend-panel:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: grab;
  user-select: none;
}

.panel-header:active {
  cursor: grabbing;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.drag-indicator {
  color: #9ca3af;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 2px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.panel-header:hover .drag-indicator {
  opacity: 1;
}

/* 面板内容区域 */
.draggable-legend-panel > div:not(.panel-header) {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .draggable-legend-panel {
    width: 280px;
  }
}

@media (max-width: 480px) {
  .draggable-legend-panel {
    width: 260px;
  }
}
</style>
