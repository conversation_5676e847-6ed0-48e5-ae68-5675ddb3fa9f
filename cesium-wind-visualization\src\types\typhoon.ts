// 台风数据类型定义

export interface TyphoonPoint {
  /** 时间 (UTC) */
  time: string;
  /** 经度 */
  longitude: number;
  /** 纬度 */
  latitude: number;
  /** 最大风速 (m/s) */
  maxWindSpeed: number;
  /** 中心气压 (hPa) */
  centralPressure: number;
  /** 台风等级 */
  category: string;
  /** 移动方向 (度) */
  direction?: number;
  /** 移动速度 (km/h) */
  speed?: number;
}

export interface TyphoonData {
  /** 台风ID */
  id: string;
  /** 台风名称 */
  name: string;
  /** 台风年份 */
  year: number;
  /** 开始时间 (UTC) */
  startTime: string;
  /** 结束时间 (UTC) */
  endTime: string;
  /** 台风路径点 */
  points: TyphoonPoint[];
  /** 最大风速 */
  maxWindSpeed: number;
  /** 最低气压 */
  minPressure: number;
  /** 影响区域 */
  affectedRegions?: string[];
}

export interface TyphoonListItem {
  id: string;
  name: string;
  year: number;
  startTime: string;
  endTime: string;
  maxWindSpeed: number;
  category: string;
}
