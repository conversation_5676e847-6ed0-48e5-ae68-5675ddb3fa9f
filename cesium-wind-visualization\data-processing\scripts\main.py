"""
主数据处理脚本
协调整个数据获取和处理流程
"""
import os
import sys
import logging
import argparse
from datetime import datetime
from config import ensure_directories

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境"""
    logger.info("设置数据处理环境...")
    
    # 创建必要的目录
    ensure_directories()
    logger.info("目录结构创建完成")
    
    # 检查Python依赖
    try:
        import cdsapi
        import xarray
        import numpy
        import pandas
        logger.info("Python依赖检查通过")
    except ImportError as e:
        logger.error(f"缺少必要的Python依赖: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    return True

def download_data(typhoon_id=None, download_era5=True, download_ibtracs=True):
    """下载数据"""
    logger.info("开始数据下载...")
    
    success = True
    
    if download_ibtracs:
        logger.info("下载IBTrACS数据...")
        from download_ibtracs import IBTrACSDownloader
        
        downloader = IBTrACSDownloader()
        if not downloader.download_western_pacific_data((2019, 2019)):
            logger.error("IBTrACS数据下载失败")
            success = False
    
    if download_era5 and success:
        logger.info("下载ERA5数据...")
        from download_era5 import ERA5Downloader
        
        downloader = ERA5Downloader()
        if typhoon_id:
            if not downloader.download_typhoon_data(typhoon_id):
                logger.error(f"ERA5数据下载失败: {typhoon_id}")
                success = False
        else:
            if not downloader.download_typhoon_data('lekima_2019'):
                logger.error("ERA5数据下载失败")
                success = False
    
    return success

def process_data(typhoon_id=None, process_era5=True, process_ibtracs=True):
    """处理数据"""
    logger.info("开始数据处理...")
    
    success = True
    
    if process_ibtracs:
        logger.info("处理IBTrACS数据...")
        from process_ibtracs import IBTrACSProcessor
        
        processor = IBTrACSProcessor()
        if typhoon_id:
            if not processor.process_typhoon_ibtracs(typhoon_id):
                logger.error(f"IBTrACS数据处理失败: {typhoon_id}")
                success = False
        else:
            if not processor.process_typhoon_ibtracs('lekima_2019'):
                logger.error("IBTrACS数据处理失败")
                success = False
        
        # 生成台风列表
        processor.generate_typhoon_list()
    
    if process_era5 and success:
        logger.info("处理ERA5数据...")
        from process_era5 import ERA5Processor
        
        processor = ERA5Processor()
        if typhoon_id:
            if not processor.process_typhoon_era5(typhoon_id):
                logger.error(f"ERA5数据处理失败: {typhoon_id}")
                success = False
        else:
            if not processor.process_typhoon_era5('lekima_2019'):
                logger.error("ERA5数据处理失败")
                success = False
    
    return success

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='台风数据获取和处理系统')
    parser.add_argument('--typhoon', type=str, help='指定台风ID (默认: lekima_2019)')
    parser.add_argument('--download-only', action='store_true', help='仅下载数据')
    parser.add_argument('--process-only', action='store_true', help='仅处理数据')
    parser.add_argument('--skip-era5', action='store_true', help='跳过ERA5数据')
    parser.add_argument('--skip-ibtracs', action='store_true', help='跳过IBTrACS数据')
    parser.add_argument('--setup-only', action='store_true', help='仅设置环境')
    
    args = parser.parse_args()
    
    # 设置环境
    if not setup_environment():
        sys.exit(1)
    
    if args.setup_only:
        logger.info("环境设置完成")
        sys.exit(0)
    
    typhoon_id = args.typhoon or 'lekima_2019'
    download_era5 = not args.skip_era5
    download_ibtracs = not args.skip_ibtracs
    process_era5 = not args.skip_era5
    process_ibtracs = not args.skip_ibtracs
    
    success = True
    
    # 下载数据
    if not args.process_only:
        if not download_data(typhoon_id, download_era5, download_ibtracs):
            logger.error("数据下载失败")
            success = False
    
    # 处理数据
    if not args.download_only and success:
        if not process_data(typhoon_id, process_era5, process_ibtracs):
            logger.error("数据处理失败")
            success = False
    
    if success:
        logger.info("数据获取和处理完成！")
        logger.info("数据文件位置:")
        logger.info(f"  - 台风数据: public/data/typhoons/")
        logger.info(f"  - 风场数据: public/data/windfields/{typhoon_id}/")
    else:
        logger.error("数据获取和处理失败")
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
