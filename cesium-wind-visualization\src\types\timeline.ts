// 时间轴相关类型定义

export interface TimelineState {
  /** 当前时间 (UTC) */
  currentTime: string;
  /** 开始时间 (UTC) */
  startTime: string;
  /** 结束时间 (UTC) */
  endTime: string;
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 播放速度倍率 */
  playbackRate: number;
  /** 可用的播放速度 */
  availableRates: number[];
  /** 时间步长 (小时) */
  timeStep: number;
}

export interface TimelineControls {
  /** 播放/暂停 */
  togglePlay: () => void;
  /** 停止 */
  stop: () => void;
  /** 快进到下一个时间点 */
  stepForward: () => void;
  /** 后退到上一个时间点 */
  stepBackward: () => void;
  /** 设置播放速度 */
  setPlaybackRate: (rate: number) => void;
  /** 跳转到指定时间 */
  jumpToTime: (time: string) => void;
}

export interface TimelineEvent {
  /** 事件类型 */
  type: "timeChange" | "playStateChange" | "rateChange";
  /** 事件数据 */
  data: any;
  /** 时间戳 */
  timestamp: number;
}

export type TimelineEventCallback = (event: TimelineEvent) => void;

// 新增：数据状态接口
export interface DataState {
  /** 当前数据点索引 */
  currentIndex: number;
  /** 下一数据点索引 */
  nextIndex: number;
  /** 插值因子 (0-1) */
  interpolationFactor: number;
  /** 当前系统时间 */
  currentTime: Date;
  /** 对应的数据时间 */
  dataTime: Date;
}

// 新增：时间控制器事件类型
export interface TimeControllerEvents {
  timeupdate: CustomEvent<DataState>;
  play: CustomEvent<void>;
  pause: CustomEvent<void>;
  speedchange: CustomEvent<number>;
  stop: CustomEvent<void>;
}

// 新增：时间控制器配置
export interface TimeControllerConfig {
  /** 数据开始时间 */
  dataStartTime: Date;
  /** 数据结束时间 */
  dataEndTime: Date;
  /** 数据间隔（小时） */
  dataIntervalHours: number;
  /** 总数据点数 */
  totalDataPoints: number;
  /** 默认播放速度 */
  defaultPlaybackSpeed: number;
  /** 可用播放速度 */
  availablePlaybackSpeeds: number[];
  /** 是否循环播放 */
  loopPlayback: boolean;
}
