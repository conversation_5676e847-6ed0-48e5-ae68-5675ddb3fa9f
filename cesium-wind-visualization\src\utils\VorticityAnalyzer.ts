import type { WindFieldData } from "@/types/windData";

/**
 * 风场涡度分析器
 * 用于检测风场数据中的涡旋中心位置
 */
export class VorticityAnalyzer {
  /**
   * 计算风场的涡度中心
   */
  static findVorticityCenter(windData: WindFieldData): {
    longitude: number;
    latitude: number;
    maxVorticity: number;
  } | null {
    if (!windData || !windData.u || !windData.v) {
      console.warn("VorticityAnalyzer: 无效的风场数据");
      return null;
    }

    const { u, v, width, height, bounds } = windData;
    const uArray = u.array;
    const vArray = v.array;

    // 移除调试日志，减少控制台噪音
    // if (process.env.NODE_ENV === "development") {
    //   console.log(
    //     `🔍 VorticityAnalyzer: 分析风场数据 ${width}x${height}, 数组长度: u=${uArray.length}, v=${vArray.length}`
    //   );
    // }

    // 验证数据完整性
    if (uArray.length !== vArray.length || uArray.length !== width * height) {
      console.error(
        `VorticityAnalyzer: 数据尺寸不匹配 - 期望: ${width * height}, 实际: u=${
          uArray.length
        }, v=${vArray.length}`
      );
      return null;
    }

    // 计算经纬度步长
    const lonStep = (bounds.east - bounds.west) / (width - 1);
    const latStep = (bounds.north - bounds.south) / (height - 1);

    let maxVorticity = 0;
    let maxVorticityAbs = 0; // 记录最大绝对值涡度
    let centerLon = bounds.west;
    let centerLat = bounds.south;
    let validPoints = 0;
    let minVorticity = Infinity;
    let avgVorticity = 0;
    let vorticitySum = 0;

    // 遍历网格点计算涡度
    for (let j = 1; j < height - 1; j++) {
      for (let i = 1; i < width - 1; i++) {
        // 计算当前点的涡度 (简化的有限差分)
        const vorticity = this.calculateVorticity(
          uArray,
          vArray,
          i,
          j,
          width,
          height,
          lonStep,
          latStep
        );

        if (isFinite(vorticity) && Math.abs(vorticity) > 1e-10) {
          // 过滤掉接近零的值
          validPoints++;
          vorticitySum += vorticity;

          // 寻找绝对值最大的涡度（台风中心）
          const absVorticity = Math.abs(vorticity);
          if (absVorticity > maxVorticityAbs) {
            maxVorticityAbs = absVorticity;
            maxVorticity = vorticity; // 保留原始符号
            centerLon = bounds.west + i * lonStep;
            centerLat = bounds.south + j * latStep;
          }

          // 统计最大最小值
          if (vorticity > minVorticity) {
            minVorticity = vorticity;
          }
        }
      }
    }

    avgVorticity = validPoints > 0 ? vorticitySum / validPoints : 0;

    // 移除详细调试日志，减少控制台噪音
    // if (process.env.NODE_ENV === "development") {
    //   console.log(
    //     `🔍 VorticityAnalyzer: 有效涡度点数: ${validPoints}, 涡度范围: [${maxVorticity.toFixed(
    //       6
    //     )} ~ ${minVorticity.toFixed(6)}], 平均: ${avgVorticity.toFixed(
    //       6
    //     )}, 最大绝对值: ${maxVorticityAbs.toFixed(
    //       6
    //     )}, 中心: [${centerLon.toFixed(3)}, ${centerLat.toFixed(3)}]`
    //   );
    // }

    // 检查是否找到有效的涡度中心（只要有足够的有效点和显著涡度即可）
    if (validPoints === 0 || maxVorticityAbs < 1e-8) {
      console.warn(
        `VorticityAnalyzer: 未找到有效的涡度中心 - 有效点数: ${validPoints}, 最大绝对涡度: ${maxVorticityAbs}`
      );
      return null;
    }

    return {
      longitude: centerLon,
      latitude: centerLat,
      maxVorticity: maxVorticityAbs, // 返回绝对值
    };
  }

  /**
   * 计算单点涡度
   */
  private static calculateVorticity(
    uArray: Float32Array,
    vArray: Float32Array,
    i: number,
    j: number,
    width: number,
    height: number,
    lonStep: number,
    latStep: number
  ): number {
    // 检查边界
    if (i <= 0 || i >= width - 1 || j <= 0 || j >= height - 1) {
      return 0;
    }

    // 获取周围点的索引
    const left = j * width + (i - 1);
    const right = j * width + (i + 1);
    const up = (j + 1) * width + i;
    const down = (j - 1) * width + i;

    // 验证索引有效性
    const maxIndex = uArray.length - 1;
    if (left < 0 || right > maxIndex || up > maxIndex || down < 0) {
      return 0;
    }

    // 获取风速分量值
    const u_up = uArray[up];
    const u_down = uArray[down];
    const v_left = vArray[left];
    const v_right = vArray[right];

    // 检查数据有效性
    if (
      !isFinite(u_up) ||
      !isFinite(u_down) ||
      !isFinite(v_left) ||
      !isFinite(v_right)
    ) {
      return 0;
    }

    // 检查步长是否有效
    if (lonStep <= 0 || latStep <= 0) {
      return 0;
    }

    // 计算偏导数
    const dvdx = (v_right - v_left) / (2 * lonStep);
    const dudy = (u_up - u_down) / (2 * latStep);

    // 涡度 = dv/dx - du/dy
    const vorticity = dvdx - dudy;

    // 检查结果是否有效
    if (!isFinite(vorticity) || Math.abs(vorticity) > 1e6) {
      return 0;
    }

    return vorticity;
  }

  /**
   * 寻找风速最小值中心（台风眼）
   */
  static findWindSpeedMinimum(windData: WindFieldData): {
    longitude: number;
    latitude: number;
    minSpeed: number;
  } | null {
    if (!windData || !windData.u || !windData.v) {
      console.warn("VorticityAnalyzer: 无效的风场数据");
      return null;
    }

    const { u, v, width, height, bounds } = windData;
    const uArray = u.array;
    const vArray = v.array;

    // 验证数据完整性
    if (uArray.length !== vArray.length || uArray.length !== width * height) {
      console.error(`VorticityAnalyzer: 风速分析数据尺寸不匹配`);
      return null;
    }

    // 计算经纬度步长
    const lonStep = (bounds.east - bounds.west) / (width - 1);
    const latStep = (bounds.north - bounds.south) / (height - 1);

    let minSpeed = Infinity;
    let centerLon = bounds.west;
    let centerLat = bounds.south;
    let validPoints = 0;

    // 遍历网格点寻找最小风速
    for (let j = 0; j < height; j++) {
      for (let i = 0; i < width; i++) {
        const index = j * width + i;

        if (index >= uArray.length || index >= vArray.length) {
          continue;
        }

        const u_val = uArray[index];
        const v_val = vArray[index];

        if (isFinite(u_val) && isFinite(v_val)) {
          const speed = Math.sqrt(u_val * u_val + v_val * v_val);

          if (isFinite(speed)) {
            validPoints++;
            if (speed < minSpeed) {
              minSpeed = speed;
              centerLon = bounds.west + i * lonStep;
              centerLat = bounds.south + j * latStep;
            }
          }
        }
      }
    }

    // 移除风速分析调试日志，减少控制台噪音
    // if (process.env.NODE_ENV === "development") {
    //   console.log(
    //     `🔍 VorticityAnalyzer: 风速分析 - 有效点数: ${validPoints}, 最小风速: ${minSpeed.toFixed(
    //       2
    //     )} m/s, 位置: [${centerLon.toFixed(3)}, ${centerLat.toFixed(3)}]`
    //   );
    // }

    if (validPoints === 0 || !isFinite(minSpeed)) {
      console.warn("VorticityAnalyzer: 未找到有效的风速最小值");
      return null;
    }

    return {
      longitude: centerLon,
      latitude: centerLat,
      minSpeed: minSpeed,
    };
  }

  /**
   * 寻找局部风速最小值（在指定区域内）
   */
  static findLocalWindSpeedMinimum(
    windData: WindFieldData,
    centerLon: number,
    centerLat: number,
    searchRadius: number = 2.0 // 搜索半径（度）
  ): {
    longitude: number;
    latitude: number;
    minSpeed: number;
  } | null {
    if (!windData || !windData.u || !windData.v) {
      return null;
    }

    const { u, v, width, height, bounds } = windData;
    const uArray = u.array;
    const vArray = v.array;

    // 计算经纬度步长
    const lonStep = (bounds.east - bounds.west) / (width - 1);
    const latStep = (bounds.north - bounds.south) / (height - 1);

    // 计算搜索范围
    const minLon = Math.max(bounds.west, centerLon - searchRadius);
    const maxLon = Math.min(bounds.east, centerLon + searchRadius);
    const minLat = Math.max(bounds.south, centerLat - searchRadius);
    const maxLat = Math.min(bounds.north, centerLat + searchRadius);

    // 转换为网格索引
    const minI = Math.max(0, Math.floor((minLon - bounds.west) / lonStep));
    const maxI = Math.min(
      width - 1,
      Math.ceil((maxLon - bounds.west) / lonStep)
    );
    const minJ = Math.max(0, Math.floor((minLat - bounds.south) / latStep));
    const maxJ = Math.min(
      height - 1,
      Math.ceil((maxLat - bounds.south) / latStep)
    );

    let minSpeed = Infinity;
    let resultLon = centerLon;
    let resultLat = centerLat;

    // 在指定区域内搜索
    for (let j = minJ; j <= maxJ; j++) {
      for (let i = minI; i <= maxI; i++) {
        const index = j * width + i;
        const speed = Math.sqrt(
          uArray[index] * uArray[index] + vArray[index] * vArray[index]
        );

        if (speed < minSpeed) {
          minSpeed = speed;
          resultLon = bounds.west + i * lonStep;
          resultLat = bounds.south + j * latStep;
        }
      }
    }

    return {
      longitude: resultLon,
      latitude: resultLat,
      minSpeed: minSpeed,
    };
  }

  /**
   * 计算两点之间的距离（公里）
   */
  static calculateDistance(
    lon1: number,
    lat1: number,
    lon2: number,
    lat2: number
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * 综合分析：比较台风中心与风场中心
   */
  static analyzeTyphoonWindFieldAlignment(
    windData: WindFieldData,
    typhoonLon: number,
    typhoonLat: number
  ): {
    vorticityCenter: {
      longitude: number;
      latitude: number;
      maxVorticity: number;
    } | null;
    windSpeedMinimum: {
      longitude: number;
      latitude: number;
      minSpeed: number;
    } | null;
    localWindSpeedMinimum: {
      longitude: number;
      latitude: number;
      minSpeed: number;
    } | null;
    distances: {
      typhoonToVorticity: number;
      typhoonToGlobalMin: number;
      typhoonToLocalMin: number;
    };
  } {
    const vorticityCenter = this.findVorticityCenter(windData);
    const windSpeedMinimum = this.findWindSpeedMinimum(windData);
    const localWindSpeedMinimum = this.findLocalWindSpeedMinimum(
      windData,
      typhoonLon,
      typhoonLat
    );

    const distances = {
      typhoonToVorticity: vorticityCenter
        ? this.calculateDistance(
            typhoonLon,
            typhoonLat,
            vorticityCenter.longitude,
            vorticityCenter.latitude
          )
        : -1,
      typhoonToGlobalMin: windSpeedMinimum
        ? this.calculateDistance(
            typhoonLon,
            typhoonLat,
            windSpeedMinimum.longitude,
            windSpeedMinimum.latitude
          )
        : -1,
      typhoonToLocalMin: localWindSpeedMinimum
        ? this.calculateDistance(
            typhoonLon,
            typhoonLat,
            localWindSpeedMinimum.longitude,
            localWindSpeedMinimum.latitude
          )
        : -1,
    };

    return {
      vorticityCenter,
      windSpeedMinimum,
      localWindSpeedMinimum,
      distances,
    };
  }
}
