import { Viewer, ImageryLayer, SingleTileImageryProvider, Rectangle, Color } from "cesium";
import type { WindFieldData } from "@/types/windData";
import { Logger } from "./Logger";

/**
 * 风场强度热力图管理器
 * 生成基于风速的热力图覆盖层
 */
export class WindIntensityHeatmap {
  private viewer: Viewer;
  private imageryLayer: ImageryLayer | null = null;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor(viewer: Viewer) {
    this.viewer = viewer;
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * 生成风速热力图
   */
  generateHeatmap(windData: WindFieldData, opacity: number = 0.6): void {
    const startTime = performance.now();
    
    // 设置画布尺寸
    this.canvas.width = windData.width;
    this.canvas.height = windData.height;

    // 计算风速
    const windSpeeds = this.calculateWindSpeeds(windData);
    const { min, max } = this.getWindSpeedRange(windSpeeds);

    // 绘制热力图
    this.drawHeatmap(windSpeeds, windData.width, windData.height, min, max);

    // 创建图像提供者
    const imageProvider = new SingleTileImageryProvider({
      url: this.canvas.toDataURL(),
      rectangle: Rectangle.fromDegrees(
        windData.bounds.west,
        windData.bounds.south,
        windData.bounds.east,
        windData.bounds.north
      ),
    });

    // 移除旧的热力图层
    if (this.imageryLayer) {
      this.viewer.imageryLayers.remove(this.imageryLayer);
    }

    // 添加新的热力图层
    this.imageryLayer = this.viewer.imageryLayers.add(imageProvider);
    this.imageryLayer.alpha = opacity;

    Logger.performance('风速热力图生成', startTime);
    Logger.success(`热力图已生成 - 风速范围: ${min.toFixed(1)}-${max.toFixed(1)} m/s`);
  }

  /**
   * 计算风速
   */
  private calculateWindSpeeds(windData: WindFieldData): Float32Array {
    const { u, v, width, height } = windData;
    const speeds = new Float32Array(width * height);

    for (let i = 0; i < width * height; i++) {
      const uVal = u.array[i];
      const vVal = v.array[i];
      speeds[i] = Math.sqrt(uVal * uVal + vVal * vVal);
    }

    return speeds;
  }

  /**
   * 获取风速范围
   */
  private getWindSpeedRange(speeds: Float32Array): { min: number; max: number } {
    let min = Infinity;
    let max = -Infinity;

    for (let i = 0; i < speeds.length; i++) {
      if (isFinite(speeds[i])) {
        min = Math.min(min, speeds[i]);
        max = Math.max(max, speeds[i]);
      }
    }

    return { min: min === Infinity ? 0 : min, max: max === -Infinity ? 0 : max };
  }

  /**
   * 绘制热力图
   */
  private drawHeatmap(
    speeds: Float32Array,
    width: number,
    height: number,
    minSpeed: number,
    maxSpeed: number
  ): void {
    const imageData = this.ctx.createImageData(width, height);
    const data = imageData.data;

    for (let i = 0; i < speeds.length; i++) {
      const speed = speeds[i];
      const normalized = (speed - minSpeed) / (maxSpeed - minSpeed);
      const color = this.getHeatmapColor(normalized);

      const pixelIndex = i * 4;
      data[pixelIndex] = color.r;     // Red
      data[pixelIndex + 1] = color.g; // Green
      data[pixelIndex + 2] = color.b; // Blue
      data[pixelIndex + 3] = color.a; // Alpha
    }

    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * 获取热力图颜色
   */
  private getHeatmapColor(normalized: number): { r: number; g: number; b: number; a: number } {
    // 确保值在0-1范围内
    normalized = Math.max(0, Math.min(1, normalized));

    // 使用彩虹色谱：蓝色(低) -> 绿色 -> 黄色 -> 红色(高)
    let r, g, b;

    if (normalized < 0.25) {
      // 蓝色到青色
      const t = normalized / 0.25;
      r = 0;
      g = Math.floor(255 * t);
      b = 255;
    } else if (normalized < 0.5) {
      // 青色到绿色
      const t = (normalized - 0.25) / 0.25;
      r = 0;
      g = 255;
      b = Math.floor(255 * (1 - t));
    } else if (normalized < 0.75) {
      // 绿色到黄色
      const t = (normalized - 0.5) / 0.25;
      r = Math.floor(255 * t);
      g = 255;
      b = 0;
    } else {
      // 黄色到红色
      const t = (normalized - 0.75) / 0.25;
      r = 255;
      g = Math.floor(255 * (1 - t));
      b = 0;
    }

    return {
      r: Math.floor(r),
      g: Math.floor(g),
      b: Math.floor(b),
      a: normalized > 0.1 ? 200 : 0, // 低风速区域透明
    };
  }

  /**
   * 设置热力图透明度
   */
  setOpacity(opacity: number): void {
    if (this.imageryLayer) {
      this.imageryLayer.alpha = Math.max(0, Math.min(1, opacity));
    }
  }

  /**
   * 显示/隐藏热力图
   */
  setVisible(visible: boolean): void {
    if (this.imageryLayer) {
      this.imageryLayer.show = visible;
    }
  }

  /**
   * 移除热力图
   */
  remove(): void {
    if (this.imageryLayer) {
      this.viewer.imageryLayers.remove(this.imageryLayer);
      this.imageryLayer = null;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.remove();
  }
}

/**
 * 风场等值线管理器
 * 生成风速等值线
 */
export class WindContourLines {
  private viewer: Viewer;
  private entities: any[] = [];

  constructor(viewer: Viewer) {
    this.viewer = viewer;
  }

  /**
   * 生成风速等值线
   */
  generateContours(windData: WindFieldData, levels: number[] = [10, 20, 30, 40, 50]): void {
    this.clearContours();

    const speeds = this.calculateWindSpeeds(windData);
    
    // 这里可以实现等值线算法（如 Marching Squares）
    // 为简化，这里只实现基本的等值线标记
    this.generateContourMarkers(windData, speeds, levels);

    Logger.success(`风速等值线已生成，共${levels.length}个等级`);
  }

  /**
   * 计算风速
   */
  private calculateWindSpeeds(windData: WindFieldData): Float32Array {
    const { u, v, width, height } = windData;
    const speeds = new Float32Array(width * height);

    for (let i = 0; i < width * height; i++) {
      const uVal = u.array[i];
      const vVal = v.array[i];
      speeds[i] = Math.sqrt(uVal * uVal + vVal * vVal);
    }

    return speeds;
  }

  /**
   * 生成等值线标记点
   */
  private generateContourMarkers(
    windData: WindFieldData,
    speeds: Float32Array,
    levels: number[]
  ): void {
    const { width, height, bounds } = windData;
    const lonStep = (bounds.east - bounds.west) / (width - 1);
    const latStep = (bounds.north - bounds.south) / (height - 1);

    // 采样点，避免过多标记
    const sampleStep = Math.max(1, Math.floor(width / 20));

    for (let j = 0; j < height; j += sampleStep) {
      for (let i = 0; i < width; i += sampleStep) {
        const index = j * width + i;
        const speed = speeds[index];

        // 找到最接近的等值线级别
        const closestLevel = levels.reduce((prev, curr) => 
          Math.abs(curr - speed) < Math.abs(prev - speed) ? curr : prev
        );

        // 如果风速接近等值线级别，添加标记
        if (Math.abs(speed - closestLevel) < 2) {
          const lon = bounds.west + i * lonStep;
          const lat = bounds.south + j * latStep;

          this.addContourMarker(lon, lat, closestLevel);
        }
      }
    }
  }

  /**
   * 添加等值线标记
   */
  private addContourMarker(longitude: number, latitude: number, level: number): void {
    const entity = this.viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude, 100),
      label: {
        text: level.toString(),
        font: '10pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 1,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      },
    });

    this.entities.push(entity);
  }

  /**
   * 清除等值线
   */
  clearContours(): void {
    this.entities.forEach(entity => {
      this.viewer.entities.remove(entity);
    });
    this.entities = [];
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.clearContours();
  }
}
