import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TimelineState } from '@/types/timeline'

export const useUIStore = defineStore('ui', () => {
  // 面板显示状态
  const showDataPanel = ref(true)
  const showParticleControlPanel = ref(true)
  const showWindDataLegend = ref(true)
  const showTimelineControls = ref(true)

  // 时间轴状态
  const timelineState = ref<TimelineState>({
    currentTime: '',
    startTime: '',
    endTime: '',
    isPlaying: false,
    playbackRate: 1,
    availableRates: [0.5, 1, 2, 5],
    timeStep: 1 // 1小时
  })

  // 选中的数据信息
  const selectedWindInfo = ref<{
    longitude: number
    latitude: number
    windSpeed: number
    windDirection: number
    temperature?: number
    pressure: string
  } | null>(null)

  // 动作
  const toggleDataPanel = () => {
    showDataPanel.value = !showDataPanel.value
  }

  const toggleParticleControlPanel = () => {
    showParticleControlPanel.value = !showParticleControlPanel.value
  }

  const toggleWindDataLegend = () => {
    showWindDataLegend.value = !showWindDataLegend.value
  }

  const toggleTimelineControls = () => {
    showTimelineControls.value = !showTimelineControls.value
  }

  const updateTimelineState = (newState: Partial<TimelineState>) => {
    timelineState.value = { ...timelineState.value, ...newState }
  }

  const setSelectedWindInfo = (info: typeof selectedWindInfo.value) => {
    selectedWindInfo.value = info
  }

  const clearSelectedWindInfo = () => {
    selectedWindInfo.value = null
  }

  // 初始化时间轴
  const initializeTimeline = (startTime: string, endTime: string) => {
    timelineState.value.startTime = startTime
    timelineState.value.endTime = endTime
    timelineState.value.currentTime = startTime
    timelineState.value.isPlaying = false
  }

  return {
    // 状态
    showDataPanel,
    showParticleControlPanel,
    showWindDataLegend,
    showTimelineControls,
    timelineState,
    selectedWindInfo,
    
    // 动作
    toggleDataPanel,
    toggleParticleControlPanel,
    toggleWindDataLegend,
    toggleTimelineControls,
    updateTimelineState,
    setSelectedWindInfo,
    clearSelectedWindInfo,
    initializeTimeline
  }
})
