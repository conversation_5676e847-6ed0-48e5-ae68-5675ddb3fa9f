// 插值计算工具函数

/**
 * 线性插值
 */
export function linearInterpolation(x0: number, y0: number, x1: number, y1: number, x: number): number {
  if (x1 === x0) return y0
  return y0 + (y1 - y0) * (x - x0) / (x1 - x0)
}

/**
 * 双线性插值
 */
export function bilinearInterpolation(
  x0: number, y0: number, x1: number, y1: number,
  q00: number, q10: number, q01: number, q11: number,
  x: number, y: number
): number {
  const r1 = linearInterpolation(x0, q00, x1, q10, x)
  const r2 = linearInterpolation(x0, q01, x1, q11, x)
  return linearInterpolation(y0, r1, y1, r2, y)
}

/**
 * 时间插值 - 用于粒子位置的平滑过渡
 */
export function timeInterpolation(
  startTime: string,
  endTime: string,
  currentTime: string,
  startValue: number,
  endValue: number
): number {
  const start = new Date(startTime).getTime()
  const end = new Date(endTime).getTime()
  const current = new Date(currentTime).getTime()
  
  if (current <= start) return startValue
  if (current >= end) return endValue
  
  const ratio = (current - start) / (end - start)
  return startValue + (endValue - startValue) * ratio
}

/**
 * 向量插值 - 用于风场向量的插值
 */
export function vectorInterpolation(
  u1: number, v1: number,
  u2: number, v2: number,
  ratio: number
): { u: number; v: number } {
  return {
    u: u1 + (u2 - u1) * ratio,
    v: v1 + (v2 - v1) * ratio
  }
}

/**
 * 球面线性插值 - 用于地理坐标的插值
 */
export function sphericalLinearInterpolation(
  lon1: number, lat1: number,
  lon2: number, lat2: number,
  ratio: number
): { longitude: number; latitude: number } {
  // 简化版本，适用于小范围的地理坐标插值
  const deltaLon = lon2 - lon1
  const deltaLat = lat2 - lat1
  
  // 处理经度跨越180度的情况
  let adjustedDeltaLon = deltaLon
  if (Math.abs(deltaLon) > 180) {
    adjustedDeltaLon = deltaLon > 0 ? deltaLon - 360 : deltaLon + 360
  }
  
  return {
    longitude: lon1 + adjustedDeltaLon * ratio,
    latitude: lat1 + deltaLat * ratio
  }
}

/**
 * 计算风向角度
 */
export function calculateWindDirection(u: number, v: number): number {
  // 风向是风来的方向，需要加180度
  let direction = Math.atan2(u, v) * 180 / Math.PI + 180
  if (direction >= 360) direction -= 360
  return direction
}

/**
 * 计算风速
 */
export function calculateWindSpeed(u: number, v: number): number {
  return Math.sqrt(u * u + v * v)
}

/**
 * 平滑插值函数 - 使用三次贝塞尔曲线
 */
export function smoothInterpolation(t: number): number {
  // 三次贝塞尔曲线 ease-in-out
  return t * t * (3 - 2 * t)
}

/**
 * 粒子轨迹插值 - 用于粒子在时间轴上的平滑移动
 */
export function particleTrajectoryInterpolation(
  positions: Array<{ x: number; y: number; time: string }>,
  currentTime: string
): { x: number; y: number } | null {
  if (positions.length === 0) return null
  if (positions.length === 1) return { x: positions[0].x, y: positions[0].y }
  
  const currentTimestamp = new Date(currentTime).getTime()
  
  // 找到当前时间所在的时间段
  for (let i = 0; i < positions.length - 1; i++) {
    const startTime = new Date(positions[i].time).getTime()
    const endTime = new Date(positions[i + 1].time).getTime()
    
    if (currentTimestamp >= startTime && currentTimestamp <= endTime) {
      const ratio = (currentTimestamp - startTime) / (endTime - startTime)
      const smoothRatio = smoothInterpolation(ratio)
      
      return {
        x: positions[i].x + (positions[i + 1].x - positions[i].x) * smoothRatio,
        y: positions[i].y + (positions[i + 1].y - positions[i].y) * smoothRatio
      }
    }
  }
  
  // 如果时间超出范围，返回最近的位置
  if (currentTimestamp < new Date(positions[0].time).getTime()) {
    return { x: positions[0].x, y: positions[0].y }
  } else {
    const last = positions[positions.length - 1]
    return { x: last.x, y: last.y }
  }
}
