<template>
  <div
    class="time-control-panel fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 shadow-lg z-50">
    <!-- 横向布局的时间控制面板 -->
    <div class="horizontal-layout">
      <!-- 左侧：时间信息 -->
      <div class="time-info">
        <div v-if="!hasWindData" class="no-data-state">
          <span class="text-gray-400">📊 未加载风场数据</span>
        </div>
        <div v-else class="current-time">
          <span class="text-gray-400">当前时间:</span>
          <span class="font-mono text-blue-400 text-lg ml-2">
            {{ formatTime(currentDataTime || windStore.selectedTime) }}
          </span>
        </div>
      </div>

      <!-- 中间：播放控制 -->
      <div v-if="hasWindData && canPlay" class="play-controls">
        <button @click="togglePlayPause" class="play-btn">
          {{ isPlaying ? "⏸️" : "▶️" }}
        </button>

        <!-- 播放进度条 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${playbackProgress * 100}%` }"></div>
          </div>
          <div class="progress-text">
            {{ currentIndex + 1 }} / {{ totalDataPoints }}
          </div>
        </div>
      </div>

      <!-- 右侧：速度控制和数据信息 -->
      <div v-if="hasWindData" class="controls-info">
        <!-- 速度控制 -->
        <div v-if="canPlay" class="speed-controls">
          <span class="text-gray-400 text-sm">速度:</span>
          <div class="speed-buttons">
            <button
              v-for="speed in availableSpeeds"
              :key="speed"
              @click="setSpeed(speed)"
              :class="['speed-btn', currentSpeed === speed ? 'active' : '']">
              {{ speed }}x
            </button>
          </div>
        </div>

        <!-- 数据信息 -->
        <div class="data-info">
          <div class="info-item">
            <span class="text-gray-400 text-xs">范围:</span>
            <span class="font-mono text-green-400 text-xs">{{
              dataTimeRange
            }}</span>
          </div>
          <div class="info-item">
            <span class="text-gray-400 text-xs">状态:</span>
            <span
              :class="isPlaying ? 'text-green-400' : 'text-gray-400'"
              class="font-mono text-xs">
              {{ playbackStatus }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <button
      @click="$emit('close')"
      class="absolute top-2 right-2 text-gray-400 hover:text-white">
      ✕
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useWindStore } from "@/stores/windStore";
import {
  InterpolatedTimeController,
  type InterpolatedTimeState,
} from "@/utils/InterpolatedTimeController";

// 定义事件
defineEmits<{
  close: [];
}>();

// 使用stores
const windStore = useWindStore();

// 插值时间控制器
let interpolatedTimeController: InterpolatedTimeController | null = null;

// 响应式状态
const isPlaying = ref(false);
const currentSpeed = ref(1);
const availableSpeeds = [0.5, 1, 2, 5];

// 时间状态
const currentDataTime = ref<string>("");
const currentIndex = ref(0);
// const nextIndex = ref(0); // 未使用
const playbackProgress = ref(0);

// 计算属性
const hasWindData = computed(() => windStore.hasWindData);

const canPlay = computed(
  () => hasWindData.value && windStore.availableTimes.length > 1
);

const totalDataPoints = computed(() => windStore.availableTimes.length);

const dataTimeRange = computed(() => {
  if (!hasWindData.value) return "无数据";
  const times = windStore.availableTimes;
  if (times.length === 0) return "无数据";
  if (times.length === 1) return "单时间点";

  const start = new Date(times[0]).toISOString().slice(5, 16).replace("T", " ");
  const end = new Date(times[times.length - 1])
    .toISOString()
    .slice(5, 16)
    .replace("T", " ");
  return `${start} ~ ${end}`;
});

const playbackStatus = computed(() => {
  if (!hasWindData.value) return "无数据";
  if (!canPlay.value) return "单时间点";
  if (isPlaying.value) {
    return `播放中 (${currentSpeed.value}x)`;
  }
  return "已暂停";
});

// 初始化插值时间控制器
const initializeInterpolatedTimeController = () => {
  if (!hasWindData.value || windStore.availableTimes.length <= 1) {
    return;
  }

  // 销毁旧的控制器
  if (interpolatedTimeController) {
    interpolatedTimeController.destroy();
  }

  // 创建新的插值控制器
  interpolatedTimeController = new InterpolatedTimeController(
    windStore.availableTimes
  );

  // 监听播放状态变化
  interpolatedTimeController.addEventListener("play", () => {
    isPlaying.value = true;
  });

  interpolatedTimeController.addEventListener("pause", () => {
    isPlaying.value = false;
  });

  interpolatedTimeController.addEventListener("stop", () => {
    isPlaying.value = false;
  });

  // 监听数据切换事件
  interpolatedTimeController.addEventListener("datachange", (event: Event) => {
    const customEvent = event as CustomEvent<{
      currentDataTime: string;
      nextDataTime: string;
      dataIndex: number;
    }>;
    const data = customEvent.detail;

    // 加载当前时间点的风场数据
    if (data.currentDataTime !== windStore.selectedTime) {
      windStore.loadWindData(
        data.currentDataTime,
        windStore.selectedPressureLevel
      );
      console.log(`数据切换: ${data.currentDataTime} → ${data.nextDataTime}`);
    }

    // 预加载下一时间点的数据
    preloadNextWindData(data.nextDataTime);
  });

  // 监听插值更新事件
  interpolatedTimeController.addEventListener(
    "interpolationupdate",
    (event: Event) => {
      const customEvent = event as CustomEvent<InterpolatedTimeState>;
      const state = customEvent.detail;

      currentDataTime.value = state.currentFrame.systemTime;
      currentIndex.value = state.dataIndex;
      playbackProgress.value = state.progress;

      // 更新WindLayerInterpolated的插值状态
      updateWindLayerInterpolation(state.currentFrame.interpolationFactor);

      // 发送时间更新事件给台风路径管理器
      updateTyphoonPathTime(state.currentFrame.systemTime);

      // console.log(
      //   `🕐 插值更新: 因子=${state.currentFrame.interpolationFactor.toFixed(
      //     3
      //   )}, 系统时间=${state.currentFrame.systemTime}, 当前数据=${
      //     state.currentFrame.currentDataTime
      //   }, 下一数据=${state.currentFrame.nextDataTime}`
      // );
    }
  );

  // console.log("插值时间控制器已初始化");
};

// 控制方法
const play = () => {
  if (!canPlay.value || !interpolatedTimeController) return;
  interpolatedTimeController.play();
};

const pause = () => {
  if (!interpolatedTimeController) return;
  interpolatedTimeController.pause();
};

// const stop = () => {
//   if (!interpolatedTimeController) return;
//   interpolatedTimeController.stop();
// };

const togglePlayPause = () => {
  if (isPlaying.value) {
    pause();
  } else {
    play();
  }
};

const setSpeed = (speed: number) => {
  if (!canPlay.value || !interpolatedTimeController) return;
  currentSpeed.value = speed;
  interpolatedTimeController.setPlaybackSpeed(speed);
};

// 预加载下一时间点的风场数据
const preloadNextWindData = async (nextDataTime: string) => {
  try {
    // 预加载下一时间点的数据
    const nextWindData = await windStore.preloadWindData(
      nextDataTime,
      windStore.selectedPressureLevel
    );

    if (nextWindData) {
      // 通知CesiumViewer设置插值状态
      const event = new CustomEvent("setInterpolationState", {
        detail: {
          currentData: windStore.currentWindData,
          nextData: nextWindData,
          interpolationFactor: 0,
        },
      });
      window.dispatchEvent(event);

      console.log(`下一数据已预加载: ${nextDataTime}`);
    }
  } catch (error) {
    console.error("预加载下一数据失败:", error);
  }
};

// 更新WindLayerInterpolated的插值状态
const updateWindLayerInterpolation = (interpolationFactor: number) => {
  // 通过事件通知CesiumViewer更新插值状态
  const event = new CustomEvent("updateInterpolation", {
    detail: { interpolationFactor },
  });
  window.dispatchEvent(event);
};

// 更新台风路径时间
const updateTyphoonPathTime = (currentTime: string) => {
  // 通过事件通知CesiumViewer更新台风路径
  const event = new CustomEvent("timeUpdate", {
    detail: { currentTime },
  });
  window.dispatchEvent(event);
};

// 格式化时间显示
const formatTime = (time: string) => {
  if (!time) return "未选择";
  return new Date(time)
    .toISOString()
    .replace("T", " ")
    .replace(".000Z", " UTC");
};

// 监听数据变化
watch(
  () => windStore.availableTimes,
  (newTimes, oldTimes) => {
    // 检查时间列表是否真的发生了变化
    const timesChanged = JSON.stringify(newTimes) !== JSON.stringify(oldTimes);

    if (newTimes && newTimes.length > 1 && timesChanged) {
      // console.log(`时间列表已更新: ${newTimes.length} 个时间点`);
      initializeInterpolatedTimeController();
    } else if (!newTimes || newTimes.length <= 1) {
      // 销毁控制器如果没有足够的时间点
      if (interpolatedTimeController) {
        interpolatedTimeController.destroy();
        interpolatedTimeController = null;
        // console.log("插值时间控制器已销毁 - 数据不足");
      }
    }
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  console.log("数据驱动时间控制面板已加载");
});

onUnmounted(() => {
  if (interpolatedTimeController) {
    interpolatedTimeController.destroy();
    interpolatedTimeController = null;
  }
  console.log("数据驱动时间控制面板已卸载");
});
</script>

<style scoped>
.time-control-panel {
  font-family: "Courier New", monospace;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.horizontal-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  min-height: 60px;
}

.time-info {
  flex: 1;
  min-width: 200px;
}

.no-data-state {
  display: flex;
  align-items: center;
}

.current-time {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.play-controls {
  flex: 2;
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: center;
}

.play-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: scale(1.05);
}

.progress-container {
  flex: 1;
  max-width: 400px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #9ca3af;
}

.controls-info {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.speed-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.speed-buttons {
  display: flex;
  gap: 5px;
}

.speed-btn {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.speed-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.speed-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #1d4ed8;
}

.data-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.font-mono {
  font-family: "Courier New", monospace;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .horizontal-layout {
    flex-direction: column;
    gap: 15px;
    min-height: auto;
  }

  .time-info,
  .play-controls,
  .controls-info {
    flex: none;
    width: 100%;
  }

  .play-controls {
    justify-content: center;
  }

  .controls-info {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .time-control-panel {
    padding: 12px;
  }

  .current-time {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .controls-info {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
