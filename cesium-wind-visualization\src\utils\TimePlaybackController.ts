/**
 * 时间播放控制器 - 完全数据驱动
 * 不包含任何硬编码数据，完全基于传入的时间列表进行播放控制
 */

export interface TimePlaybackState {
  currentIndex: number;
  currentTime: string;
  nextIndex: number;
  nextTime: string | null;
  isPlaying: boolean;
  playbackSpeed: number;
  progress: number; // 0-1之间的播放进度
  totalDuration: number; // 总时长（毫秒）
  elapsedTime: number; // 已播放时长（毫秒）
}

export interface TimePlaybackOptions {
  timeList: string[]; // 时间列表，完全来自数据
  initialIndex?: number;
  playbackSpeed?: number;
  loop?: boolean;
  autoPlay?: boolean;
}

export class TimePlaybackController extends EventTarget {
  private timeList: string[] = [];
  private currentIndex: number = 0;
  private isPlaying: boolean = false;
  private playbackSpeed: number = 1;
  private loop: boolean = true;
  private intervalId: number | null = null;
  private startTime: number = 0;
  private pausedTime: number = 0;

  constructor(options: TimePlaybackOptions) {
    super();
    this.timeList = [...options.timeList]; // 复制数组，避免外部修改
    this.currentIndex = options.initialIndex || 0;
    this.playbackSpeed = options.playbackSpeed || 1;
    this.loop = options.loop !== false; // 默认循环播放

    // 验证时间列表
    if (this.timeList.length === 0) {
      throw new Error("时间列表不能为空");
    }

    // 确保索引有效
    this.currentIndex = Math.max(
      0,
      Math.min(this.currentIndex, this.timeList.length - 1)
    );

    console.log(
      `TimePlaybackController 初始化: ${this.timeList.length} 个时间点`
    );

    if (options.autoPlay && this.timeList.length > 1) {
      this.play();
    }
  }

  /**
   * 开始播放
   */
  play(): void {
    if (this.isPlaying || this.timeList.length <= 1) {
      return;
    }

    this.isPlaying = true;
    this.startTime = Date.now() - this.pausedTime;

    // 计算播放间隔：实现1秒=1小时的映射
    // 数据间隔是3小时，所以需要3秒播放一个数据点来实现1秒=1小时
    const DATA_INTERVAL_HOURS = 3; // ERA5数据间隔3小时
    const REAL_SECOND_TO_SYSTEM_HOUR = 1; // 1现实秒 = 1系统小时
    const baseInterval = DATA_INTERVAL_HOURS * 1000; // 3秒播放一个数据点
    const actualInterval = baseInterval / this.playbackSpeed;

    this.intervalId = window.setInterval(() => {
      this.nextTimeStep();
    }, actualInterval);

    this.dispatchEvent(
      new CustomEvent("play", {
        detail: this.getCurrentState(),
      })
    );

    console.log(
      `开始播放: 速度 ${this.playbackSpeed}x, 间隔 ${actualInterval}ms`
    );
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (!this.isPlaying) {
      return;
    }

    this.isPlaying = false;
    this.pausedTime = Date.now() - this.startTime;

    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.dispatchEvent(
      new CustomEvent("pause", {
        detail: this.getCurrentState(),
      })
    );

    console.log("播放已暂停");
  }

  /**
   * 停止播放并重置到开始
   */
  stop(): void {
    this.pause();
    this.currentIndex = 0;
    this.pausedTime = 0;
    this.startTime = 0;

    this.dispatchEvent(
      new CustomEvent("stop", {
        detail: this.getCurrentState(),
      })
    );

    console.log("播放已停止并重置");
  }

  /**
   * 设置播放速度
   */
  setPlaybackSpeed(speed: number): void {
    if (speed <= 0) {
      console.warn("播放速度必须大于0");
      return;
    }

    const wasPlaying = this.isPlaying;
    if (wasPlaying) {
      this.pause();
    }

    this.playbackSpeed = speed;

    if (wasPlaying) {
      this.play();
    }

    this.dispatchEvent(
      new CustomEvent("speedchange", {
        detail: { speed, state: this.getCurrentState() },
      })
    );

    console.log(`播放速度已设置为: ${speed}x`);
  }

  /**
   * 跳转到指定时间索引
   */
  seekToIndex(index: number): void {
    if (index < 0 || index >= this.timeList.length) {
      console.warn(`无效的时间索引: ${index}`);
      return;
    }

    this.currentIndex = index;
    this.pausedTime = 0;
    this.startTime = Date.now();

    this.dispatchEvent(
      new CustomEvent("seek", {
        detail: this.getCurrentState(),
      })
    );

    console.log(`跳转到时间索引: ${index} (${this.timeList[index]})`);
  }

  /**
   * 跳转到指定时间
   */
  seekToTime(time: string): void {
    const index = this.timeList.indexOf(time);
    if (index === -1) {
      console.warn(`未找到指定时间: ${time}`);
      return;
    }

    this.seekToIndex(index);
  }

  /**
   * 下一个时间步
   */
  private nextTimeStep(): void {
    if (this.currentIndex >= this.timeList.length - 1) {
      if (this.loop) {
        this.currentIndex = 0;
        this.startTime = Date.now();
        this.pausedTime = 0;
      } else {
        this.pause();
        return;
      }
    } else {
      this.currentIndex++;
    }

    this.dispatchEvent(
      new CustomEvent("timeupdate", {
        detail: this.getCurrentState(),
      })
    );
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): TimePlaybackState {
    const totalDuration =
      this.timeList.length > 1
        ? ((this.timeList.length - 1) * 1000) / this.playbackSpeed
        : 0;

    const elapsedTime = this.isPlaying
      ? Date.now() - this.startTime
      : this.pausedTime;

    const progress =
      totalDuration > 0 ? Math.min(1, elapsedTime / totalDuration) : 0;

    return {
      currentIndex: this.currentIndex,
      currentTime: this.timeList[this.currentIndex],
      nextIndex:
        this.currentIndex < this.timeList.length - 1
          ? this.currentIndex + 1
          : this.loop
          ? 0
          : this.currentIndex,
      nextTime:
        this.currentIndex < this.timeList.length - 1
          ? this.timeList[this.currentIndex + 1]
          : this.loop
          ? this.timeList[0]
          : null,
      isPlaying: this.isPlaying,
      playbackSpeed: this.playbackSpeed,
      progress,
      totalDuration,
      elapsedTime,
    };
  }

  /**
   * 更新时间列表（当数据变化时）
   */
  updateTimeList(newTimeList: string[]): void {
    if (newTimeList.length === 0) {
      console.warn("新的时间列表不能为空");
      return;
    }

    const wasPlaying = this.isPlaying;
    if (wasPlaying) {
      this.pause();
    }

    this.timeList = [...newTimeList];
    this.currentIndex = Math.max(
      0,
      Math.min(this.currentIndex, this.timeList.length - 1)
    );
    this.pausedTime = 0;
    this.startTime = Date.now();

    console.log(`时间列表已更新: ${this.timeList.length} 个时间点`);

    this.dispatchEvent(
      new CustomEvent("timelistupdate", {
        detail: { timeList: this.timeList, state: this.getCurrentState() },
      })
    );

    if (wasPlaying && this.timeList.length > 1) {
      this.play();
    }
  }

  /**
   * 销毁控制器
   */
  destroy(): void {
    this.pause();
    this.timeList = [];
    console.log("TimePlaybackController 已销毁");
  }

  /**
   * 获取时间列表
   */
  getTimeList(): string[] {
    return [...this.timeList];
  }

  /**
   * 检查是否可以播放
   */
  canPlay(): boolean {
    return this.timeList.length > 1;
  }
}
