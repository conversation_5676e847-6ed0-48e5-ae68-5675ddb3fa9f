/**
 * 统一时间步进控制器
 * 解决时间映射和插值问题，实现真正的1秒=1小时
 */

export interface TimeStep {
  systemTime: string; // 系统时间 (插值后的时间)
  dataTime: string;   // 数据时间 (实际数据点时间)
  interpolationFactor: number; // 插值因子 0-1
  dataIndex: number;  // 数据索引
  nextDataIndex: number; // 下一个数据索引
}

export interface UnifiedTimeState {
  currentStep: TimeStep;
  isPlaying: boolean;
  playbackSpeed: number;
  progress: number;
  totalSteps: number;
  currentStepIndex: number;
}

export class UnifiedTimeController extends EventTarget {
  private dataTimeList: string[] = []; // 原始数据时间列表 (3小时间隔)
  private systemTimeSteps: TimeStep[] = []; // 系统时间步列表 (1小时间隔)
  private currentStepIndex: number = 0;
  private isPlaying: boolean = false;
  private playbackSpeed: number = 1;
  private intervalId: number | null = null;
  
  // 配置常量
  private readonly REAL_SECOND_TO_SYSTEM_HOUR = 1; // 1现实秒 = 1系统小时
  private readonly DATA_INTERVAL_HOURS = 3; // 数据间隔3小时
  private readonly INTERPOLATION_STEPS = 3; // 每个数据间隔插值3步

  constructor(dataTimeList: string[]) {
    super();
    this.updateDataTimeList(dataTimeList);
  }

  /**
   * 更新数据时间列表并生成插值时间步
   */
  updateDataTimeList(dataTimeList: string[]): void {
    this.dataTimeList = [...dataTimeList];
    this.generateSystemTimeSteps();
    this.currentStepIndex = 0;
    
    console.log(`统一时间控制器已更新: ${this.dataTimeList.length} 个数据点, ${this.systemTimeSteps.length} 个系统时间步`);
  }

  /**
   * 生成系统时间步 (在数据点之间插值)
   */
  private generateSystemTimeSteps(): void {
    this.systemTimeSteps = [];
    
    if (this.dataTimeList.length === 0) return;
    
    for (let i = 0; i < this.dataTimeList.length - 1; i++) {
      const currentDataTime = new Date(this.dataTimeList[i]);
      const nextDataTime = new Date(this.dataTimeList[i + 1]);
      
      // 在两个数据点之间生成插值步骤
      for (let step = 0; step < this.INTERPOLATION_STEPS; step++) {
        const interpolationFactor = step / this.INTERPOLATION_STEPS;
        const systemTime = new Date(
          currentDataTime.getTime() + 
          (nextDataTime.getTime() - currentDataTime.getTime()) * interpolationFactor
        );
        
        this.systemTimeSteps.push({
          systemTime: systemTime.toISOString(),
          dataTime: this.dataTimeList[i],
          interpolationFactor,
          dataIndex: i,
          nextDataIndex: i + 1
        });
      }
    }
    
    // 添加最后一个数据点
    if (this.dataTimeList.length > 0) {
      const lastIndex = this.dataTimeList.length - 1;
      this.systemTimeSteps.push({
        systemTime: this.dataTimeList[lastIndex],
        dataTime: this.dataTimeList[lastIndex],
        interpolationFactor: 0,
        dataIndex: lastIndex,
        nextDataIndex: lastIndex
      });
    }
  }

  /**
   * 开始播放
   */
  play(): void {
    if (this.isPlaying || this.systemTimeSteps.length === 0) return;
    
    this.isPlaying = true;
    
    // 计算播放间隔: 1秒对应1小时，考虑播放速度
    const baseInterval = 1000; // 1秒
    const actualInterval = baseInterval / this.playbackSpeed;
    
    this.intervalId = window.setInterval(() => {
      this.nextTimeStep();
    }, actualInterval);
    
    this.dispatchEvent(new CustomEvent('play', {
      detail: this.getCurrentState()
    }));
    
    console.log(`统一时间播放开始: 速度 ${this.playbackSpeed}x, 间隔 ${actualInterval}ms`);
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (!this.isPlaying) return;
    
    this.isPlaying = false;
    
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.dispatchEvent(new CustomEvent('pause', {
      detail: this.getCurrentState()
    }));
  }

  /**
   * 停止播放并重置
   */
  stop(): void {
    this.pause();
    this.currentStepIndex = 0;
    
    this.dispatchEvent(new CustomEvent('stop', {
      detail: this.getCurrentState()
    }));
  }

  /**
   * 设置播放速度
   */
  setPlaybackSpeed(speed: number): void {
    if (speed <= 0) return;
    
    const wasPlaying = this.isPlaying;
    if (wasPlaying) this.pause();
    
    this.playbackSpeed = speed;
    
    if (wasPlaying) this.play();
    
    this.dispatchEvent(new CustomEvent('speedchange', {
      detail: { speed, state: this.getCurrentState() }
    }));
  }

  /**
   * 下一个时间步
   */
  private nextTimeStep(): void {
    if (this.currentStepIndex >= this.systemTimeSteps.length - 1) {
      // 循环播放
      this.currentStepIndex = 0;
    } else {
      this.currentStepIndex++;
    }
    
    this.dispatchEvent(new CustomEvent('timeupdate', {
      detail: this.getCurrentState()
    }));
  }

  /**
   * 跳转到指定步骤
   */
  seekToStep(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.systemTimeSteps.length) return;
    
    this.currentStepIndex = stepIndex;
    
    this.dispatchEvent(new CustomEvent('seek', {
      detail: this.getCurrentState()
    }));
  }

  /**
   * 跳转到指定系统时间
   */
  seekToSystemTime(systemTime: string): void {
    const stepIndex = this.systemTimeSteps.findIndex(step => 
      step.systemTime === systemTime
    );
    
    if (stepIndex !== -1) {
      this.seekToStep(stepIndex);
    }
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): UnifiedTimeState {
    const currentStep = this.systemTimeSteps[this.currentStepIndex] || {
      systemTime: '',
      dataTime: '',
      interpolationFactor: 0,
      dataIndex: 0,
      nextDataIndex: 0
    };
    
    const progress = this.systemTimeSteps.length > 0 ? 
      this.currentStepIndex / (this.systemTimeSteps.length - 1) : 0;
    
    return {
      currentStep,
      isPlaying: this.isPlaying,
      playbackSpeed: this.playbackSpeed,
      progress,
      totalSteps: this.systemTimeSteps.length,
      currentStepIndex: this.currentStepIndex
    };
  }

  /**
   * 获取所有系统时间步
   */
  getSystemTimeSteps(): TimeStep[] {
    return [...this.systemTimeSteps];
  }

  /**
   * 检查是否可以播放
   */
  canPlay(): boolean {
    return this.systemTimeSteps.length > 1;
  }

  /**
   * 销毁控制器
   */
  destroy(): void {
    this.pause();
    this.dataTimeList = [];
    this.systemTimeSteps = [];
    console.log('统一时间控制器已销毁');
  }
}
