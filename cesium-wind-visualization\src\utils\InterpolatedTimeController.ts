/**
 * 插值时间控制器 - 实现真正的平滑过渡
 * 在数据点之间进行实时插值，消除跳跃效果
 */

export interface InterpolationFrame {
  currentDataTime: string;
  nextDataTime: string;
  interpolationFactor: number; // 0-1之间
  systemTime: string; // 当前显示的系统时间
}

export interface InterpolatedTimeState {
  currentFrame: InterpolationFrame;
  isPlaying: boolean;
  playbackSpeed: number;
  dataIndex: number;
  totalDataPoints: number;
  progress: number;
}

export class InterpolatedTimeController extends EventTarget {
  private dataTimeList: string[] = [];
  private currentDataIndex: number = 0;
  private isPlaying: boolean = false;
  private playbackSpeed: number = 1;
  private animationId: number | null = null;
  private startTime: number = 0;
  private pausedTime: number = 0;

  // 配置常量
  private readonly DATA_INTERVAL_HOURS = 3; // 数据间隔3小时
  private readonly REAL_SECOND_TO_SYSTEM_HOUR = 1; // 1现实秒 = 1系统小时
  private readonly INTERPOLATION_DURATION = 3000; // 3秒完成一个数据间隔的插值

  constructor(dataTimeList: string[]) {
    super();
    this.updateDataTimeList(dataTimeList);
  }

  /**
   * 更新数据时间列表
   */
  updateDataTimeList(dataTimeList: string[]): void {
    this.dataTimeList = [...dataTimeList];
    this.currentDataIndex = 0;
    this.pausedTime = 0;

    console.log(`插值时间控制器已更新: ${this.dataTimeList.length} 个数据点`);
  }

  /**
   * 开始播放
   */
  play(): void {
    if (this.isPlaying || this.dataTimeList.length <= 1) {
      return;
    }

    this.isPlaying = true;
    this.startTime = performance.now() - this.pausedTime;

    // 启动动画循环
    this.startAnimationLoop();

    this.dispatchEvent(
      new CustomEvent("play", {
        detail: this.getCurrentState(),
      })
    );

    console.log(`插值播放开始: 速度 ${this.playbackSpeed}x`);
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (!this.isPlaying) return;

    this.isPlaying = false;
    this.pausedTime = performance.now() - this.startTime;

    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    this.dispatchEvent(
      new CustomEvent("pause", {
        detail: this.getCurrentState(),
      })
    );
  }

  /**
   * 停止播放并重置
   */
  stop(): void {
    this.pause();
    this.currentDataIndex = 0;
    this.pausedTime = 0;
    this.startTime = 0;

    this.dispatchEvent(
      new CustomEvent("stop", {
        detail: this.getCurrentState(),
      })
    );
  }

  /**
   * 设置播放速度
   */
  setPlaybackSpeed(speed: number): void {
    if (speed <= 0) return;

    const wasPlaying = this.isPlaying;

    // 如果正在播放，保持当前的数据索引和插值因子
    if (wasPlaying) {
      const currentTime = performance.now();
      const oldElapsedTime =
        (currentTime - this.startTime) * this.playbackSpeed;

      // 计算当前在哪个数据间隔以及插值进度
      const oldIntervalDuration =
        this.INTERPOLATION_DURATION / this.playbackSpeed;
      const currentInterval = Math.floor(oldElapsedTime / oldIntervalDuration);
      const intervalProgress =
        (oldElapsedTime % oldIntervalDuration) / oldIntervalDuration;

      // 更新播放速度
      this.playbackSpeed = speed;

      // 计算新的间隔时长
      const newIntervalDuration = this.INTERPOLATION_DURATION / speed;

      // 重新计算startTime以保持相同的数据索引和插值因子
      const targetElapsedTime =
        currentInterval * newIntervalDuration +
        intervalProgress * newIntervalDuration;
      this.startTime = currentTime - targetElapsedTime / speed;
    } else {
      // 如果暂停中，保持当前的数据索引和插值因子
      const oldElapsedTime = this.pausedTime * this.playbackSpeed;
      const oldIntervalDuration =
        this.INTERPOLATION_DURATION / this.playbackSpeed;
      const currentInterval = Math.floor(oldElapsedTime / oldIntervalDuration);
      const intervalProgress =
        (oldElapsedTime % oldIntervalDuration) / oldIntervalDuration;

      // 更新播放速度
      this.playbackSpeed = speed;

      // 计算新的间隔时长和pausedTime
      const newIntervalDuration = this.INTERPOLATION_DURATION / speed;
      const targetElapsedTime =
        currentInterval * newIntervalDuration +
        intervalProgress * newIntervalDuration;
      this.pausedTime = targetElapsedTime / speed;
    }

    this.dispatchEvent(
      new CustomEvent("speedchange", {
        detail: { speed, state: this.getCurrentState() },
      })
    );
  }

  /**
   * 动画循环 - 每帧更新插值状态
   */
  private startAnimationLoop(): void {
    const animate = () => {
      if (!this.isPlaying) return;

      const currentTime = performance.now();
      const elapsedTime = (currentTime - this.startTime) * this.playbackSpeed;

      // 计算当前应该在哪个数据间隔
      const intervalDuration = this.INTERPOLATION_DURATION / this.playbackSpeed;
      const currentInterval = Math.floor(elapsedTime / intervalDuration);
      const intervalProgress =
        (elapsedTime % intervalDuration) / intervalDuration;

      // 更新数据索引
      const newDataIndex = currentInterval % this.dataTimeList.length;
      if (newDataIndex !== this.currentDataIndex) {
        this.currentDataIndex = newDataIndex;

        // 发送数据切换事件
        this.dispatchEvent(
          new CustomEvent("datachange", {
            detail: {
              currentDataTime: this.dataTimeList[this.currentDataIndex],
              nextDataTime: this.getNextDataTime(),
              dataIndex: this.currentDataIndex,
            },
          })
        );
      }

      // 发送插值更新事件
      this.dispatchEvent(
        new CustomEvent("interpolationupdate", {
          detail: this.getCurrentState(),
        })
      );

      // 继续动画循环
      this.animationId = requestAnimationFrame(animate);
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * 获取下一个数据时间
   */
  private getNextDataTime(): string {
    const nextIndex = (this.currentDataIndex + 1) % this.dataTimeList.length;
    return this.dataTimeList[nextIndex];
  }

  /**
   * 计算当前插值因子
   */
  private calculateInterpolationFactor(): number {
    let elapsedTime: number;

    if (this.isPlaying) {
      // 播放中：基于当前时间计算
      const currentTime = performance.now();
      elapsedTime = (currentTime - this.startTime) * this.playbackSpeed;
    } else {
      // 暂停中：基于暂停时的时间计算，保持当前状态
      elapsedTime = this.pausedTime * this.playbackSpeed;
    }

    const intervalDuration = this.INTERPOLATION_DURATION / this.playbackSpeed;
    const intervalProgress =
      (elapsedTime % intervalDuration) / intervalDuration;

    return Math.min(1, Math.max(0, intervalProgress));
  }

  /**
   * 计算当前系统时间
   */
  private calculateSystemTime(): string {
    const interpolationFactor = this.calculateInterpolationFactor();
    const currentDataTime = new Date(this.dataTimeList[this.currentDataIndex]);
    const nextDataTime = new Date(this.getNextDataTime());

    // 在两个数据时间之间插值
    const timeDiff = nextDataTime.getTime() - currentDataTime.getTime();
    const interpolatedTime = new Date(
      currentDataTime.getTime() + timeDiff * interpolationFactor
    );

    return interpolatedTime.toISOString();
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): InterpolatedTimeState {
    const interpolationFactor = this.calculateInterpolationFactor();
    const systemTime = this.calculateSystemTime();

    const currentFrame: InterpolationFrame = {
      currentDataTime: this.dataTimeList[this.currentDataIndex] || "",
      nextDataTime: this.getNextDataTime(),
      interpolationFactor,
      systemTime,
    };

    const progress =
      this.dataTimeList.length > 0
        ? (this.currentDataIndex + interpolationFactor) /
          this.dataTimeList.length
        : 0;

    return {
      currentFrame,
      isPlaying: this.isPlaying,
      playbackSpeed: this.playbackSpeed,
      dataIndex: this.currentDataIndex,
      totalDataPoints: this.dataTimeList.length,
      progress: Math.min(1, progress),
    };
  }

  /**
   * 跳转到指定数据索引
   */
  seekToDataIndex(index: number): void {
    if (index < 0 || index >= this.dataTimeList.length) return;

    this.currentDataIndex = index;
    this.pausedTime = 0;
    this.startTime = performance.now();

    this.dispatchEvent(
      new CustomEvent("seek", {
        detail: this.getCurrentState(),
      })
    );
  }

  /**
   * 检查是否可以播放
   */
  canPlay(): boolean {
    return this.dataTimeList.length > 1;
  }

  /**
   * 获取时间列表
   */
  getTimeList(): string[] {
    return [...this.dataTimeList];
  }

  /**
   * 销毁控制器
   */
  destroy(): void {
    this.pause();
    this.dataTimeList = [];
    console.log("插值时间控制器已销毁");
  }
}
