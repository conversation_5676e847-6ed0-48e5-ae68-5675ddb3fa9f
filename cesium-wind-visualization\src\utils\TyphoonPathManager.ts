import {
  <PERSON>er,
  <PERSON>tity,
  Cartesian3,
  Color,
  PolylineGlowMaterialProperty,
  PointGraphics,
  LabelGraphics,
  HorizontalOrigin,
  VerticalOrigin,
  JulianDate,
} from "cesium";
import type { TyphoonData, TyphoonPoint } from "@/types/typhoon";
import type { WindFieldData } from "@/types/windData";
import { VorticityAnalyzer } from "./VorticityAnalyzer";
import {
  TyphoonCenterComparison,
  TyphoonAccuracyStats,
} from "./TyphoonCenterComparison";

/**
 * 台风路径可视化管理器
 * 负责在Cesium中绘制和管理台风路径
 */
export class TyphoonPathManager {
  private viewer: Viewer;
  private typhoonData: TyphoonData | null = null;
  private pathEntity: Entity | null = null;
  private currentPositionEntity: Entity | null = null;
  private pathPoints: Entity[] = [];
  private currentTimeIndex: number = 0;

  // 中心对比功能
  private centerComparison: TyphoonCenterComparison;
  private accuracyStats: TyphoonAccuracyStats;
  private showCenterComparison: boolean = false;

  constructor(viewer: Viewer) {
    this.viewer = viewer;
    this.centerComparison = new TyphoonCenterComparison(viewer);
    this.accuracyStats = new TyphoonAccuracyStats();
  }

  /**
   * 设置台风数据
   */
  setTyphoonData(typhoonData: TyphoonData | null): void {
    // 清除旧的可视化
    this.clearVisualization();

    this.typhoonData = typhoonData;
    if (typhoonData) {
      this.initializeVisualization();
      console.log(`台风路径管理器已设置: ${typhoonData.name}`);
    }
  }

  /**
   * 根据当前时间更新台风路径显示
   */
  updateByTime(currentTime: string): void {
    if (!this.typhoonData) return;

    // 找到当前时间应该显示到哪个台风位置点
    const visibleIndex = this.findVisibleTimeIndex(currentTime);

    this.currentTimeIndex = visibleIndex;
    this.updatePathVisualization();

    // 检查是否有精确匹配的数据时间点
    const exactMatch = this.findExactTimeMatch(currentTime);
    if (exactMatch !== -1) {
      // 精确匹配：更新到精确位置
      this.currentTimeIndex = exactMatch;
      this.updateCurrentPosition();
      console.log(
        `🌀 台风位置更新: ${currentTime} -> 数据点${exactMatch} (${
          this.typhoonData.points[exactMatch].time
        }) 位置[${this.typhoonData.points[exactMatch].longitude.toFixed(
          3
        )}, ${this.typhoonData.points[exactMatch].latitude.toFixed(3)}]`
      );
    } else {
      // 插值期间：保持显示最近的已知位置，不隐藏
      // 使用visibleIndex作为当前显示的位置
      if (visibleIndex >= 0) {
        this.currentTimeIndex = visibleIndex;
        this.updateCurrentPosition();
        console.log(
          `台风位置保持: ${currentTime} -> 最近数据点${visibleIndex} (${this.typhoonData.points[visibleIndex].time})`
        );
      }
    }

    // 确保台风位置标记始终可见（如果有有效数据）
    if (this.currentPositionEntity && this.currentTimeIndex >= 0) {
      this.currentPositionEntity.show = true;
    }
  }

  /**
   * 初始化可视化
   */
  private initializeVisualization(): void {
    if (!this.typhoonData) return;

    // 创建路径实体
    this.createPathEntity();

    // 创建当前位置实体
    this.createCurrentPositionEntity();

    console.log(
      `台风路径可视化已初始化: ${this.typhoonData.points.length} 个路径点`
    );
  }

  /**
   * 创建路径实体
   */
  private createPathEntity(): void {
    if (!this.typhoonData) return;

    this.pathEntity = this.viewer.entities.add({
      id: `typhoon-path-${this.typhoonData.id}`,
      name: `${this.typhoonData.name}路径`,
      polyline: {
        positions: [], // 初始为空，动态更新
        width: 3,
        material: new PolylineGlowMaterialProperty({
          glowPower: 0.2,
          color: Color.CYAN,
        }),
        clampToGround: true,
      },
    });
  }

  /**
   * 创建当前位置实体
   */
  private createCurrentPositionEntity(): void {
    if (!this.typhoonData) return;

    this.currentPositionEntity = this.viewer.entities.add({
      id: `typhoon-current-${this.typhoonData.id}`,
      name: `${this.typhoonData.name}当前位置`,
      position: Cartesian3.fromDegrees(0, 0, 0), // 初始位置，动态更新
      point: {
        pixelSize: 15,
        color: Color.RED,
        outlineColor: Color.WHITE,
        outlineWidth: 2,
        heightReference: 0, // 贴地
      },
      label: {
        text: this.typhoonData.name,
        font: "12pt sans-serif",
        fillColor: Color.WHITE,
        outlineColor: Color.BLACK,
        outlineWidth: 2,
        horizontalOrigin: HorizontalOrigin.LEFT,
        verticalOrigin: VerticalOrigin.BOTTOM,
        pixelOffset: new Cartesian3(15, -15, 0),
      },
    });
  }

  /**
   * 更新路径可视化
   */
  private updatePathVisualization(): void {
    if (!this.pathEntity || !this.typhoonData) return;

    // 获取当前时间之前的所有路径点
    const visiblePoints = this.typhoonData.points.slice(
      0,
      this.currentTimeIndex + 1
    );

    // 转换为Cesium坐标
    const positions = visiblePoints.map((point) =>
      Cartesian3.fromDegrees(point.longitude, point.latitude, 0)
    );

    // 更新路径
    if (this.pathEntity.polyline) {
      this.pathEntity.polyline.positions = positions;
    }
  }

  /**
   * 更新当前位置
   */
  private updateCurrentPosition(): void {
    if (!this.currentPositionEntity || !this.typhoonData) return;

    const currentPoint = this.typhoonData.points[this.currentTimeIndex];
    if (!currentPoint) return;

    // 更新位置
    this.currentPositionEntity.position = Cartesian3.fromDegrees(
      currentPoint.longitude,
      currentPoint.latitude,
      0
    );

    // 更新标签信息
    if (this.currentPositionEntity.label) {
      this.currentPositionEntity.label.text =
        `${this.typhoonData.name}\n` +
        `风速: ${currentPoint.maxWindSpeed.toFixed(1)} m/s\n` +
        `气压: ${currentPoint.centralPressure.toFixed(0)} hPa\n` +
        `等级: ${currentPoint.category}\n` +
        `位置: ${currentPoint.longitude.toFixed(
          2
        )}°E, ${currentPoint.latitude.toFixed(2)}°N\n` +
        `时间: ${currentPoint.time.substring(11, 16)}`;
    }

    // 根据风速调整颜色
    if (this.currentPositionEntity.point) {
      this.currentPositionEntity.point.color = this.getColorByWindSpeed(
        currentPoint.maxWindSpeed
      );
    }
  }

  /**
   * 找到当前时间应该显示到哪个台风位置点（累积显示）
   */
  private findVisibleTimeIndex(timeString: string): number {
    if (!this.typhoonData) return -1;

    const targetTime = new Date(timeString).getTime();
    let lastVisibleIndex = -1;

    // 找到当前时间之前的最后一个台风数据点
    for (let i = 0; i < this.typhoonData.points.length; i++) {
      const pointTime = new Date(this.typhoonData.points[i].time).getTime();

      if (pointTime <= targetTime) {
        lastVisibleIndex = i;
      } else {
        break;
      }
    }

    return lastVisibleIndex;
  }

  /**
   * 检查当前时间是否精确匹配某个台风数据时间点
   */
  private findExactTimeMatch(timeString: string): number {
    if (!this.typhoonData) return -1;

    const targetTime = new Date(timeString).getTime();
    const tolerance = 30 * 60 * 1000; // 30分钟容差

    for (let i = 0; i < this.typhoonData.points.length; i++) {
      const pointTime = new Date(this.typhoonData.points[i].time).getTime();
      const diff = Math.abs(targetTime - pointTime);

      if (diff <= tolerance) {
        return i;
      }
    }

    return -1;
  }

  /**
   * 根据时间字符串找到对应的索引（保留原方法作为备用）
   */
  private findTimeIndex(timeString: string): number {
    if (!this.typhoonData) return -1;

    // 找到最接近的时间点
    const targetTime = new Date(timeString).getTime();
    let closestIndex = -1;
    let minDiff = Infinity;

    for (let i = 0; i < this.typhoonData.points.length; i++) {
      const pointTime = new Date(this.typhoonData.points[i].time).getTime();
      const diff = Math.abs(targetTime - pointTime);

      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = i;
      }
    }

    return closestIndex;
  }

  /**
   * 根据风速获取颜色
   */
  private getColorByWindSpeed(windSpeed: number): Color {
    if (windSpeed < 17.2) return Color.GREEN; // 热带低压
    if (windSpeed < 24.5) return Color.YELLOW; // 热带风暴
    if (windSpeed < 32.7) return Color.ORANGE; // 强热带风暴
    if (windSpeed < 41.5) return Color.RED; // 台风
    if (windSpeed < 51.0) return Color.PURPLE; // 强台风
    return Color.MAGENTA; // 超强台风
  }

  /**
   * 清除可视化
   */
  private clearVisualization(): void {
    // 移除路径实体
    if (this.pathEntity) {
      this.viewer.entities.remove(this.pathEntity);
      this.pathEntity = null;
    }

    // 移除当前位置实体
    if (this.currentPositionEntity) {
      this.viewer.entities.remove(this.currentPositionEntity);
      this.currentPositionEntity = null;
    }

    // 清除路径点
    this.pathPoints.forEach((entity) => {
      this.viewer.entities.remove(entity);
    });
    this.pathPoints = [];

    this.currentTimeIndex = 0;
  }

  /**
   * 显示/隐藏台风路径
   */
  setVisible(visible: boolean): void {
    if (this.pathEntity) {
      this.pathEntity.show = visible;
    }
    if (this.currentPositionEntity) {
      this.currentPositionEntity.show = visible;
    }
  }

  /**
   * 缩放到台风路径
   */
  zoomToPath(): void {
    if (!this.typhoonData || this.typhoonData.points.length === 0) return;

    // 计算边界
    let minLon = Infinity,
      maxLon = -Infinity;
    let minLat = Infinity,
      maxLat = -Infinity;

    this.typhoonData.points.forEach((point) => {
      minLon = Math.min(minLon, point.longitude);
      maxLon = Math.max(maxLon, point.longitude);
      minLat = Math.min(minLat, point.latitude);
      maxLat = Math.max(maxLat, point.latitude);
    });

    // 添加边距
    const margin = 2.0;
    const west = minLon - margin;
    const east = maxLon + margin;
    const south = minLat - margin;
    const north = maxLat + margin;

    // 缩放到边界
    this.viewer.camera.setView({
      destination: Cartesian3.fromDegrees(
        (west + east) / 2,
        (south + north) / 2,
        1000000
      ),
    });
  }

  /**
   * 分析台风中心与风场中心的偏差
   */
  analyzeWindFieldAlignment(windData: WindFieldData): any {
    if (!this.typhoonData || this.currentTimeIndex < 0) return null;

    const currentPoint = this.typhoonData.points[this.currentTimeIndex];
    if (!currentPoint) return null;

    const analysis = VorticityAnalyzer.analyzeTyphoonWindFieldAlignment(
      windData,
      currentPoint.longitude,
      currentPoint.latitude
    );

    // console.log(`🔍 台风-风场中心分析 (${currentPoint.time}):`);
    // console.log(
    //   `  台风中心: [${currentPoint.longitude.toFixed(
    //     3
    //   )}, ${currentPoint.latitude.toFixed(3)}]`
    // );

    // if (analysis.vorticityCenter) {
    //   console.log(
    //     `  涡度中心: [${analysis.vorticityCenter.longitude.toFixed(
    //       3
    //     )}, ${analysis.vorticityCenter.latitude.toFixed(
    //       3
    //     )}] 距离: ${analysis.distances.typhoonToVorticity.toFixed(1)}km`
    //   );
    // }

    // if (analysis.windSpeedMinimum) {
    //   console.log(
    //     `  全局风速最小: [${analysis.windSpeedMinimum.longitude.toFixed(
    //       3
    //     )}, ${analysis.windSpeedMinimum.latitude.toFixed(
    //       3
    //     )}] 距离: ${analysis.distances.typhoonToGlobalMin.toFixed(1)}km`
    //   );
    // }

    // if (analysis.localWindSpeedMinimum) {
    //   console.log(
    //     `  局部风速最小: [${analysis.localWindSpeedMinimum.longitude.toFixed(
    //       3
    //     )}, ${analysis.localWindSpeedMinimum.latitude.toFixed(
    //       3
    //     )}] 距离: ${analysis.distances.typhoonToLocalMin.toFixed(1)}km`
    //   );
    // }

    // 显示中心对比（如果启用）
    if (this.showCenterComparison) {
      this.centerComparison.showComparison(
        {
          longitude: currentPoint.longitude,
          latitude: currentPoint.latitude,
          name: this.typhoonData.name,
        },
        windData
      );
    }

    // 收集统计数据
    if (analysis.vorticityCenter && analysis.localWindSpeedMinimum) {
      this.accuracyStats.addStats(
        currentPoint.time,
        analysis.distances.typhoonToVorticity,
        analysis.distances.typhoonToLocalMin
      );
    }

    // 验证分析结果的合理性
    const vorticityDistance = analysis.distances?.typhoonToVorticity || -1;
    const eyeDistance = analysis.distances?.typhoonToLocalMin || -1;

    // 检查距离是否在合理范围内（0-1000km）
    const isVorticityValid = vorticityDistance > 0 && vorticityDistance < 1000;
    const isEyeValid = eyeDistance > 0 && eyeDistance < 1000;

    // console.log(
    //   `🔍 分析结果验证: 涡度距离=${vorticityDistance.toFixed(1)}km (${
    //     isVorticityValid ? "有效" : "无效"
    //   }), 台风眼距离=${eyeDistance.toFixed(1)}km (${
    //     isEyeValid ? "有效" : "无效"
    //   })`
    // );

    // 如果数据无效，输出详细的分析结果
    // if (!isVorticityValid || !isEyeValid) {
    //   console.warn("🔍 详细分析结果:", {
    //     vorticityCenter: analysis.vorticityCenter,
    //     localWindSpeedMinimum: analysis.localWindSpeedMinimum,
    //     distances: analysis.distances,
    //   });
    // }

    // 准备返回的数据
    const resultData = {
      timestamp: currentPoint.time,
      vorticityDistance: isVorticityValid ? vorticityDistance : 0,
      eyeDistance: isEyeValid ? eyeDistance : 0,
      windSpeed: currentPoint.maxWindSpeed,
      pressure: currentPoint.centralPressure,
      analysis: analysis,
      isValid: isVorticityValid && isEyeValid,
    };

    // console.log(
    //   `🔍 返回给面板的数据: 涡度=${resultData.vorticityDistance.toFixed(
    //     1
    //   )}km, 台风眼=${resultData.eyeDistance.toFixed(1)}km`
    // );

    // 返回分析结果供面板使用
    return resultData;
  }

  /**
   * 设置中心对比显示
   */
  setCenterComparisonVisible(visible: boolean): void {
    this.showCenterComparison = visible;
    this.centerComparison.setVisible(visible);
    if (!visible) {
      this.centerComparison.clearComparison();
    }
  }

  /**
   * 获取精度统计
   */
  getAccuracyStats(): any {
    return this.accuracyStats.getSummary();
  }

  /**
   * 清除统计数据
   */
  clearStats(): void {
    this.accuracyStats.clear();
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.clearVisualization();
    this.centerComparison.destroy();
    this.typhoonData = null;
    // console.log("台风路径管理器已销毁");
  }
}
