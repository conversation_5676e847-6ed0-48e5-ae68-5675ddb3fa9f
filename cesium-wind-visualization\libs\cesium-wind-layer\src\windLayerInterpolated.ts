import {
  Viewer,
  Scene,
  Cartesian2,
  SceneMode,
  Math as <PERSON><PERSON><PERSON><PERSON>,
  Rectangle,
} from "cesium";
import { WindLayerOptions, WindData, InterpolationState } from "./types";
import { WindParticleSystemInterpolated } from "./windParticleSystemInterpolated";
import { deepMerge } from "./utils";

/**
 * ✅ 增强版WindLayer - 支持时间插值
 * 独立实现，避免循环依赖问题
 */
export class WindLayerInterpolated {
  private _show: boolean = true;
  windData: Required<WindData>;

  get show(): boolean {
    return this._show;
  }

  set show(value: boolean) {
    if (this._show !== value) {
      this._show = value;
      this.updatePrimitivesVisibility(value);
    }
  }

  viewer: Viewer;
  scene: Scene;
  options: WindLayerOptions;
  private interpolatedParticleSystem: WindParticleSystemInterpolated;
  private viewerParameters: {
    lonRange: Cartesian2;
    latRange: Cartesian2;
    pixelSize: number;
    sceneMode: SceneMode;
  };
  private _isDestroyed: boolean = false;
  private primitives: any[] = [];

  // 插值状态
  private interpolationState: InterpolationState | null = null;

  constructor(
    viewer: Viewer,
    windData: WindData,
    options?: Partial<WindLayerOptions>
  ) {
    this.viewer = viewer;
    this.scene = viewer.scene;

    // 强制启用插值的默认选项
    const defaultOptions: WindLayerOptions = {
      particlesTextureSize: 100,
      dropRate: 0.003,
      particleHeight: 1000,
      dropRateBump: 0.01,
      speedFactor: 1.0,
      lineWidth: { min: 1, max: 2 },
      lineLength: { min: 20, max: 100 },
      colors: ["white"],
      flipY: false,
      useViewerBounds: false,
      domain: undefined,
      displayRange: undefined,
      dynamic: true,
      enableInterpolation: true,
    };

    this.options = { ...defaultOptions, ...options, enableInterpolation: true };
    this.windData = this.processWindData(windData);

    this.viewerParameters = {
      lonRange: new Cartesian2(-180, 180),
      latRange: new Cartesian2(-90, 90),
      pixelSize: 1000.0,
      sceneMode: this.scene.mode,
    };

    this.updateViewerParameters();
    this.initializeInterpolatedParticleSystem();
    this.add();
    this.setupEventListeners();
  }

  /**
   * ✅ 初始化插值粒子系统
   */
  private initializeInterpolatedParticleSystem(): void {
    this.interpolatedParticleSystem = new WindParticleSystemInterpolated(
      this.scene.context,
      this.windData,
      this.options,
      this.viewerParameters,
      this.scene
    );
  }

  /**
   * ✅ 处理风场数据
   */
  private processWindData(windData: WindData): Required<WindData> {
    if (
      windData.speed?.min === undefined ||
      windData.speed?.max === undefined ||
      windData.speed.array === undefined
    ) {
      const speed = {
        array: new Float32Array(windData.u.array.length),
        min: Number.MAX_VALUE,
        max: Number.MIN_VALUE,
      };
      for (let i = 0; i < windData.u.array.length; i++) {
        speed.array[i] = Math.sqrt(
          windData.u.array[i] * windData.u.array[i] +
            windData.v.array[i] * windData.v.array[i]
        );
        if (speed.array[i] !== 0) {
          speed.min = Math.min(speed.min, speed.array[i]);
          speed.max = Math.max(speed.max, speed.array[i]);
        }
      }
      windData = { ...windData, speed };
    }

    return windData as Required<WindData>;
  }

  /**
   * ✅ 设置事件监听器
   */
  private setupEventListeners(): void {
    this.viewer.camera.percentageChanged = 0.01;
    this.viewer.camera.changed.addEventListener(
      this.updateViewerParameters.bind(this)
    );
    this.scene.morphComplete.addEventListener(
      this.updateViewerParameters.bind(this)
    );
    window.addEventListener("resize", this.updateViewerParameters.bind(this));
  }

  /**
   * ✅ 移除事件监听器
   */
  private removeEventListeners(): void {
    this.viewer.camera.changed.removeEventListener(
      this.updateViewerParameters.bind(this)
    );
    this.scene.morphComplete.removeEventListener(
      this.updateViewerParameters.bind(this)
    );
    window.removeEventListener(
      "resize",
      this.updateViewerParameters.bind(this)
    );
  }

  /**
   * ✅ 更新视图参数
   */
  private updateViewerParameters(): void {
    const scene = this.viewer.scene;
    const canvas = scene.canvas;
    const corners = [
      { x: 0, y: 0 },
      { x: 0, y: canvas.clientHeight },
      { x: canvas.clientWidth, y: 0 },
      { x: canvas.clientWidth, y: canvas.clientHeight },
    ];

    // Convert screen corners to cartographic coordinates
    let minLon = 180;
    let maxLon = -180;
    let minLat = 90;
    let maxLat = -90;
    let isOutsideGlobe = false;

    for (const corner of corners) {
      const cartesian = scene.camera.pickEllipsoid(
        new Cartesian2(corner.x, corner.y),
        scene.globe.ellipsoid
      );

      if (!cartesian) {
        isOutsideGlobe = true;
        break;
      }

      const cartographic =
        scene.globe.ellipsoid.cartesianToCartographic(cartesian);
      const lon = CesiumMath.toDegrees(cartographic.longitude);
      const lat = CesiumMath.toDegrees(cartographic.latitude);

      minLon = Math.min(minLon, lon);
      maxLon = Math.max(maxLon, lon);
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
    }

    if (!isOutsideGlobe) {
      // Calculate intersection with data bounds
      const lonRange = new Cartesian2(
        Math.max(this.windData.bounds.west, minLon),
        Math.min(this.windData.bounds.east, maxLon)
      );
      const latRange = new Cartesian2(
        Math.max(this.windData.bounds.south, minLat),
        Math.min(this.windData.bounds.north, maxLat)
      );

      // Add 5% buffer to lonRange and latRange
      const lonBuffer = (lonRange.y - lonRange.x) * 0.05;
      const latBuffer = (latRange.y - latRange.x) * 0.05;

      lonRange.x = Math.max(this.windData.bounds.west, lonRange.x - lonBuffer);
      lonRange.y = Math.min(this.windData.bounds.east, lonRange.y + lonBuffer);
      latRange.x = Math.max(this.windData.bounds.south, latRange.x - latBuffer);
      latRange.y = Math.min(this.windData.bounds.north, latRange.y + latBuffer);

      this.viewerParameters.lonRange = lonRange;
      this.viewerParameters.latRange = latRange;

      // Calculate pixelSize based on the visible range
      const dataLonRange =
        this.windData.bounds.east - this.windData.bounds.west;
      const dataLatRange =
        this.windData.bounds.north - this.windData.bounds.south;

      // Calculate the ratio of visible area to total data area based on the shortest side
      const visibleRatioLon = (lonRange.y - lonRange.x) / dataLonRange;
      const visibleRatioLat = (latRange.y - latRange.x) / dataLatRange;
      const visibleRatio = Math.min(visibleRatioLon, visibleRatioLat);

      // Map the ratio to a pixelSize value between 0 and 1000
      const pixelSize = 1000 * visibleRatio;
      if (pixelSize > 0) {
        this.viewerParameters.pixelSize = Math.max(
          0,
          Math.min(1000, pixelSize)
        );
      }
    }

    this.viewerParameters.sceneMode = this.scene.mode;
    this.interpolatedParticleSystem?.applyViewerParameters(
      this.viewerParameters
    );
  }

  /**
   * ✅ 更新原语可见性
   */
  private updatePrimitivesVisibility(visibility?: boolean): void {
    const show = visibility !== undefined ? visibility : this._show;
    this.primitives.forEach((primitive) => {
      primitive.show = show;
    });
  }

  /**
   * ✅ 添加到场景
   */
  add(): void {
    this.primitives = this.interpolatedParticleSystem.getPrimitives();
    this.primitives.forEach((primitive) => {
      this.scene.primitives.add(primitive);
    });
  }

  /**
   * ✅ 从场景移除
   */
  remove(): void {
    this.primitives.forEach((primitive) => {
      this.scene.primitives.remove(primitive);
    });
    this.primitives = [];
  }

  /**
   * ✅ 设置插值状态 - 核心API
   * 这是与MasterTimeController集成的关键方法
   */
  setInterpolationState(state: InterpolationState): void {
    this.interpolationState = state;
    this.interpolatedParticleSystem.setInterpolationState(state);
    this.viewer.scene.requestRender();
  }

  /**
   * ✅ 更新插值因子 - 高频调用优化
   * 仅更新插值因子，不重新加载纹理
   */
  updateInterpolationFactor(factor: number): void {
    if (this.interpolationState) {
      this.interpolationState.interpolationFactor = factor;
      this.interpolatedParticleSystem.updateInterpolationFactor(factor);
      this.viewer.scene.requestRender();
    }
  }

  /**
   * ✅ 设置双风场数据 - 用于预加载
   * 同时设置当前和下一时间点的数据
   */
  setDualWindData(
    currentData: Required<WindData>,
    nextData: Required<WindData>,
    interpolationFactor: number = 0
  ): void {
    const state: InterpolationState = {
      currentData,
      nextData,
      interpolationFactor,
      isInterpolating: true,
    };

    this.setInterpolationState(state);
  }

  /**
   * ✅ 平滑过渡到新数据
   * 提供动画过渡效果
   */
  smoothTransitionTo(
    newData: Required<WindData>,
    duration: number = 1000
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!this.interpolationState) {
        // 如果没有当前状态，直接设置
        this.updateWindData(newData);
        resolve();
        return;
      }

      const startTime = performance.now();
      const currentData = this.interpolationState.currentData;

      const animate = () => {
        const elapsed = performance.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 更新插值因子
        this.updateInterpolationFactor(progress);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // 过渡完成，设置为新数据
          this.updateWindData(newData);
          resolve();
        }
      };

      // 设置过渡状态
      this.setDualWindData(currentData, newData, 0);
      animate();
    });
  }

  /**
   * ✅ 获取插值状态
   */
  getInterpolationState(): InterpolationState | null {
    return this.interpolationState;
  }

  /**
   * ✅ 获取插值性能统计
   */
  getInterpolationPerformanceStats(): {
    interpolationEnabled: boolean;
    interpolationFactor: number;
    textureMemoryUsage: number;
    isInterpolating: boolean;
  } {
    const baseStats = this.interpolatedParticleSystem.getPerformanceStats();

    return {
      ...baseStats,
      isInterpolating: this.interpolationState?.isInterpolating ?? false,
    };
  }

  /**
   * ✅ 启用/禁用插值模式
   */
  setInterpolationEnabled(enabled: boolean): void {
    this.interpolatedParticleSystem.setInterpolationEnabled(enabled);

    if (!enabled) {
      this.interpolationState = null;
    }

    this.viewer.scene.requestRender();
  }

  /**
   * ✅ 更新风场数据
   */
  updateWindData(data: WindData): void {
    if (this._isDestroyed) return;

    // 如果正在插值，将新数据设置为当前数据
    if (this.interpolationState?.isInterpolating) {
      this.interpolationState.currentData = this.processWindData(data);
      this.interpolationState.isInterpolating = false;
      this.interpolationState.interpolationFactor = 0;
    }

    this.windData = this.processWindData(data);
    this.interpolatedParticleSystem.computing.updateWindData(this.windData);
    this.viewer.scene.requestRender();
  }

  /**
   * ✅ 更新选项
   */
  updateOptions(options: Partial<WindLayerOptions>): void {
    if (this._isDestroyed) return;
    this.options = deepMerge(options, this.options);
    this.interpolatedParticleSystem.changeOptions(options);
    this.viewer.scene.requestRender();
  }

  /**
   * ✅ 缩放到数据范围
   */
  zoomTo(duration: number = 0): void {
    if (this.windData.bounds) {
      const rectangle = Rectangle.fromDegrees(
        this.windData.bounds.west,
        this.windData.bounds.south,
        this.windData.bounds.east,
        this.windData.bounds.north
      );
      this.viewer.camera.flyTo({
        destination: rectangle,
        duration,
      });
    }
  }

  /**
   * ✅ 检查是否已销毁
   */
  isDestroyed(): boolean {
    return this._isDestroyed;
  }

  /**
   * ✅ 销毁资源
   */
  destroy(): void {
    this.remove();
    this.removeEventListeners();
    this.interpolatedParticleSystem.destroy();
    this.interpolationState = null;
    this._isDestroyed = true;
  }
}

/**
 * ✅ 工厂函数：创建插值版WindLayer
 */
export function createInterpolatedWindLayer(
  viewer: Viewer,
  windData: WindData,
  options?: Partial<WindLayerOptions>
): WindLayerInterpolated {
  return new WindLayerInterpolated(viewer, windData, options);
}

/**
 * ✅ 工具函数：检查是否支持插值
 */
export function supportsInterpolation(viewer: Viewer): boolean {
  try {
    const context = viewer.scene.context;
    const gl = context._gl;

    // 检查WebGL扩展支持
    const floatTextureSupport = gl.getExtension("OES_texture_float");
    const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);

    return floatTextureSupport !== null && maxTextureSize >= 1024;
  } catch (error) {
    console.warn("Failed to check interpolation support:", error);
    return false;
  }
}

// 导出类型
export type { InterpolationState };
