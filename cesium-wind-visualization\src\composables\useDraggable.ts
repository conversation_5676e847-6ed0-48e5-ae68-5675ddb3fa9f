/**
 * 可拖拽组件的Vue组合式函数
 * 提供拖拽功能和位置管理
 */

import { ref, onMounted, onUnmounted, type Ref } from 'vue'

export interface DraggableOptions {
  /** 初始X位置 */
  initialX?: number
  /** 初始Y位置 */
  initialY?: number
  /** 拖拽手柄选择器 */
  handle?: string
  /** 是否限制在父容器内 */
  containment?: boolean
  /** 最小X位置 */
  minX?: number
  /** 最小Y位置 */
  minY?: number
  /** 最大X位置 */
  maxX?: number
  /** 最大Y位置 */
  maxY?: number
  /** 拖拽开始回调 */
  onDragStart?: (x: number, y: number) => void
  /** 拖拽中回调 */
  onDrag?: (x: number, y: number) => void
  /** 拖拽结束回调 */
  onDragEnd?: (x: number, y: number) => void
}

export function useDraggable(
  elementRef: Ref<HTMLElement | null>,
  options: DraggableOptions = {}
) {
  const {
    initialX = 0,
    initialY = 0,
    handle,
    containment = true,
    minX,
    minY,
    maxX,
    maxY,
    onDragStart,
    onDrag,
    onDragEnd
  } = options

  // 响应式位置状态
  const x = ref(initialX)
  const y = ref(initialY)
  const isDragging = ref(false)

  // 拖拽状态
  let startX = 0
  let startY = 0
  let startMouseX = 0
  let startMouseY = 0

  // 获取拖拽手柄元素
  const getDragHandle = (): HTMLElement | null => {
    if (!elementRef.value) return null
    
    if (handle) {
      return elementRef.value.querySelector(handle) as HTMLElement
    }
    
    return elementRef.value
  }

  // 限制位置在边界内
  const constrainPosition = (newX: number, newY: number): [number, number] => {
    let constrainedX = newX
    let constrainedY = newY

    // 应用自定义边界
    if (typeof minX === 'number') constrainedX = Math.max(constrainedX, minX)
    if (typeof minY === 'number') constrainedY = Math.max(constrainedY, minY)
    if (typeof maxX === 'number') constrainedX = Math.min(constrainedX, maxX)
    if (typeof maxY === 'number') constrainedY = Math.min(constrainedY, maxY)

    // 限制在视口内
    if (containment && elementRef.value) {
      const rect = elementRef.value.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      constrainedX = Math.max(0, Math.min(constrainedX, viewportWidth - rect.width))
      constrainedY = Math.max(0, Math.min(constrainedY, viewportHeight - rect.height))
    }

    return [constrainedX, constrainedY]
  }

  // 更新元素位置
  const updatePosition = (newX: number, newY: number) => {
    const [constrainedX, constrainedY] = constrainPosition(newX, newY)
    
    x.value = constrainedX
    y.value = constrainedY

    if (elementRef.value) {
      elementRef.value.style.left = `${constrainedX}px`
      elementRef.value.style.top = `${constrainedY}px`
    }
  }

  // 鼠标按下事件
  const handleMouseDown = (event: MouseEvent) => {
    event.preventDefault()
    
    if (!elementRef.value) return

    isDragging.value = true
    startX = x.value
    startY = y.value
    startMouseX = event.clientX
    startMouseY = event.clientY

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    // 设置拖拽样式
    elementRef.value.style.cursor = 'grabbing'
    elementRef.value.style.userSelect = 'none'
    elementRef.value.style.zIndex = '9999'

    // 触发拖拽开始回调
    onDragStart?.(x.value, y.value)
  }

  // 鼠标移动事件
  const handleMouseMove = (event: MouseEvent) => {
    if (!isDragging.value) return

    const deltaX = event.clientX - startMouseX
    const deltaY = event.clientY - startMouseY
    
    const newX = startX + deltaX
    const newY = startY + deltaY

    updatePosition(newX, newY)

    // 触发拖拽中回调
    onDrag?.(x.value, y.value)
  }

  // 鼠标释放事件
  const handleMouseUp = () => {
    if (!isDragging.value) return

    isDragging.value = false

    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)

    // 恢复样式
    if (elementRef.value) {
      elementRef.value.style.cursor = ''
      elementRef.value.style.userSelect = ''
      elementRef.value.style.zIndex = ''
    }

    // 触发拖拽结束回调
    onDragEnd?.(x.value, y.value)
  }

  // 触摸事件支持
  const handleTouchStart = (event: TouchEvent) => {
    if (event.touches.length !== 1) return
    
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    
    handleMouseDown(mouseEvent)
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (event.touches.length !== 1) return
    
    event.preventDefault()
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    
    handleMouseMove(mouseEvent)
  }

  const handleTouchEnd = () => {
    handleMouseUp()
  }

  // 初始化拖拽功能
  const initDraggable = () => {
    const dragHandle = getDragHandle()
    if (!dragHandle) return

    // 设置初始位置
    updatePosition(initialX, initialY)

    // 设置拖拽手柄样式
    dragHandle.style.cursor = 'grab'

    // 添加事件监听器
    dragHandle.addEventListener('mousedown', handleMouseDown)
    dragHandle.addEventListener('touchstart', handleTouchStart)
    dragHandle.addEventListener('touchmove', handleTouchMove)
    dragHandle.addEventListener('touchend', handleTouchEnd)
  }

  // 清理拖拽功能
  const destroyDraggable = () => {
    const dragHandle = getDragHandle()
    if (!dragHandle) return

    // 移除事件监听器
    dragHandle.removeEventListener('mousedown', handleMouseDown)
    dragHandle.removeEventListener('touchstart', handleTouchStart)
    dragHandle.removeEventListener('touchmove', handleTouchMove)
    dragHandle.removeEventListener('touchend', handleTouchEnd)

    // 清理全局事件监听器
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  // 设置位置
  const setPosition = (newX: number, newY: number) => {
    updatePosition(newX, newY)
  }

  // 重置到初始位置
  const reset = () => {
    updatePosition(initialX, initialY)
  }

  // 生命周期钩子
  onMounted(() => {
    initDraggable()
  })

  onUnmounted(() => {
    destroyDraggable()
  })

  return {
    // 响应式状态
    x,
    y,
    isDragging,
    
    // 方法
    setPosition,
    reset,
    initDraggable,
    destroyDraggable
  }
}

// 默认导出
export default useDraggable
